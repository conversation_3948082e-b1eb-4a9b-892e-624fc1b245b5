use crate::components::environment::grass::{
    generate_grass, GrassGenerationParams, GrassMaterial
};
use crate::components::environment::{Terrain, TerrainCell, TerrainPod};
use crate::post_process::pixelated_material::ExtendedPixelatedMaterial;
use crate::resources::environment::{
    EnvironmentAssets, EnvironmentAssetsKeys, TerrainPodGrid
};
use bevy::ecs::system::SystemParam;
use bevy::prelude::*;
use rand::prelude::SliceRandom;

#[derive(SystemParam)]
pub struct EnvironmentAssetSystemParams<'w> {
    environment_assets: Res<'w, EnvironmentAssets>,
    gltf_assets: Res<'w, Assets<Gltf>>,
}

impl<'w> EnvironmentAssetSystemParams<'w> {
    pub fn get_model(&self, name: &str) -> Option<SceneRoot> {
        self.environment_assets
            .get_nature_stylized_model(name, &self.gltf_assets)
    }

    pub fn get_random_rock_model(&self) -> Option<SceneRoot> {
        let rocks = [
            EnvironmentAssetsKeys::ROCK_1,
            EnvironmentAssetsKeys::ROCK_2,
            EnvironmentAssetsKeys::ROCK_3,
            EnvironmentAssetsKeys::ROCK_4,
            EnvironmentAssetsKeys::ROCK_5,
        ];

        let rock = rocks.choose(&mut rand::thread_rng())?;
        self.get_model(rock)
    }
}

#[derive(SystemParam)]
pub struct TerrainSystemParams<'w, 's> {
    pub commands: Commands<'w, 's>,
    pub terrain_pod_grid: ResMut<'w, TerrainPodGrid>,
    pub terrain_pod_query: Query<'w, 's, (&'static mut TerrainPod, &'static Children)>,
    pub terrain_cell_query: Query<
        'w,
        's,
        (&'static TerrainCell, &'static Terrain, &'static GlobalTransform),
        With<TerrainCell>,
    >,
    pub meshes: ResMut<'w, Assets<Mesh>>,
    pub materials: ResMut<'w, Assets<StandardMaterial>>,
    pub grass_materials: ResMut<'w, Assets<GrassMaterial>>,
    pub pixelated_materials: ResMut<'w, Assets<ExtendedPixelatedMaterial>>,
}

impl<'w, 's> TerrainSystemParams<'_, '_> {
    pub fn get_random_position_in_pod(&self) -> Vec3 {
        self.terrain_pod_grid.get_random_position_in_pod()
    }

    pub fn get_random_position_in_pod_with_height_range(
        &self,
        min_height: f32,
        max_height: f32,
    ) -> Vec3 {
        self.terrain_pod_grid
            .get_random_position_in_pod_with_height_range(min_height, max_height)
    }

    pub fn is_in_bounds(&self, position: Vec3) -> bool {
        let pod_index = self.terrain_pod_grid.world_to_cell(position);
        if let Some(&pod_entity) = self.terrain_pod_grid.get_pod(pod_index.0, pod_index.1)
        {
            return self.terrain_pod_query.get(pod_entity).is_ok();
        }
        false
    }

    pub fn get_terrain_moisture(&self, current_pod: Entity, position: Vec3) -> f32 {
        if let Some((_, terrain)) =
            self.get_terrain_cell_from_position(current_pod, position)
        {
            return terrain.properties.moisture;
        }

        0.0
    }

    pub fn get_terrain_cell_from_position(
        &self,
        current_pod: Entity,
        position: Vec3,
    ) -> Option<(&TerrainCell, &Terrain)> {
        if let Ok((_terrain_pod, children)) = self.terrain_pod_query.get(current_pod) {
            for child in children.iter() {
                if let Ok((terrain_cell, terrain, global_transform)) =
                    self.terrain_cell_query.get(child)
                {
                    if global_transform.translation().distance(position) < 0.5 {
                        return Some((terrain_cell, terrain));
                    }
                }
            }
        }
        None
    }

    pub fn generate_grass(&mut self, params: GrassGenerationParams) -> Entity {
        let (bundle, main_grass, main_data) =
            generate_grass(&mut self.meshes, &mut self.grass_materials, params);

        self.commands
            .spawn((Mesh3d(bundle.mesh), MeshMaterial3d(bundle.material)))
            .insert(Name::new(format!("Grass ({}, {})", params.spawn_x, params.spawn_z)))
            .insert(main_grass)
            // .insert(main_data)
            .insert(InheritedVisibility::default())
            .id()
    }

    pub fn get_center_position(&self, pod: &TerrainPod) -> Vec3 {
        let pod_width = pod.width as f32 * pod.cell_size;
        let pod_depth = pod.depth as f32 * pod.cell_size;
        let x = pod_width / 2.0;
        let z = pod_depth / 2.0;
        Vec3::new(x, 0.0, z)
    }

    pub fn get_center_of_active_pod(&self) -> Option<Vec3> {
        if let Some(active_pod) = self.terrain_pod_grid.active_pod {
            if let Some(&pod_entity) =
                self.terrain_pod_grid.get_pod(active_pod.0, active_pod.1)
            {
                if let Ok((pod, _)) = self.terrain_pod_query.get(pod_entity) {
                    return Some(self.get_center_position(pod));
                }
            }
        }

        None
    }

    pub fn get_terrain_pod_entity_from_position(&self, position: Vec3) -> Option<Entity> {
        let pod_index = self.terrain_pod_grid.world_to_cell(position);
        if let Some(&pod_entity) = self.terrain_pod_grid.get_pod(pod_index.0, pod_index.1)
        {
            return Some(pod_entity.clone());
        }

        None
    }

    pub fn get_terrain_pod_from_entity(&self, entity: Entity) -> Option<&TerrainPod> {
        self.terrain_pod_query.get(entity).ok().map(|(pod, _)| pod)
    }

    pub fn get_terrain_pod_from_position(&self, position: Vec3) -> Option<&TerrainPod> {
        let pod_entity = self.get_terrain_pod_entity_from_position(position)?;
        self.terrain_pod_query
            .get(pod_entity)
            .ok()
            .map(|(pod, _)| pod)
    }
}
