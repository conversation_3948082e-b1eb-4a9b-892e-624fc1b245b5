use crate::behaviors::common::EatFoodBehavior;
use crate::components::animals::animation_state::{
    AnimationBlendState, AnimationConfig, AnimationState, DirectionTracker, OrganismAnimation
};
use crate::components::animals::{AnimalAnimationIndex, InWater};
use crate::components::environment::{Terrain, TerrainPod};
use crate::components::lifecycle::{CurrentTerrainPod, Dying};
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::animals::animation_controller::should_change_animation;
use avian3d::prelude::LinearVelocity;
use bevy::prelude::*;
use bevy_gltf_animation::prelude::GltfAnimations;
use rand::Rng;

/// System to update direction tracking for turn detection
pub fn update_direction_tracking(
    mut organism_query: Query<(&mut DirectionTracker, &LinearVelocity)>,
    delta_time: Option<Res<WorldSimulationDeltaTime>>,
) {
    let dt = delta_time.map(|d| d.0).unwrap_or(1.0 / 60.0);

    for (mut direction_tracker, linear_velocity) in organism_query.iter_mut() {
        direction_tracker.update(linear_velocity.0, dt);
    }
}

/// System to create and manage animation graphs for blending
/// Note: Simplified approach using manual weight management instead of complex animation graphs
pub fn setup_animation_graphs(
    mut commands: Commands,
    organism_query: Query<Entity, (With<GltfAnimations>, Without<AnimationBlendState>)>,
) {
    for entity in organism_query.iter() {
        // Add blend state component for manual animation blending
        commands
            .entity(entity)
            .insert(AnimationBlendState::default());
    }
}

/// Enhanced animation system with turn blending support
pub fn blended_animation_system(
    mut organism_query: Query<(
        Entity,
        &mut AnimationState,
        &mut AnimationConfig,
        &mut DirectionTracker,
        &mut AnimationBlendState,
        &mut GltfAnimations,
        &LinearVelocity,
        Option<&Dying>,
        Option<&EatFoodBehavior>,
        Option<&CurrentTerrainPod>,
        Option<&InWater>,
    )>,
    mut animation_players: Query<&mut AnimationPlayer>,
    terrain_query: Query<&Terrain>,
    terrain_pod_query: Query<&TerrainPod>,
    delta_time: Option<Res<WorldSimulationDeltaTime>>,
) {
    let dt = delta_time.map(|d| d.0).unwrap_or(1.0 / 60.0);

    for (
        entity,
        mut animation_state,
        mut animation_config,
        mut direction_tracker,
        mut blend_state,
        mut gltf_animations,
        linear_velocity,
        dying,
        eating_behavior,
        current_pod,
        in_water,
    ) in organism_query.iter_mut()
    {
        // Update timers
        animation_state.update_time(dt);
        animation_config.update_idle_timer(dt);

        // Determine base animation
        let base_animation =
            crate::systems::animals::animation_controller::determine_base_animation(
                &linear_velocity,
                &animation_config,
                dying,
                eating_behavior,
                in_water,
                current_pod,
                &terrain_query,
                &terrain_pod_query,
            );

        // Check if we should use blending for movement animations
        let should_blend_turns =
            matches!(base_animation, OrganismAnimation::Walk | OrganismAnimation::Run)
                && direction_tracker.is_turning;

        if should_blend_turns {
            // Use blended animation system
            apply_blended_animation(
                entity,
                &mut animation_state,
                &mut blend_state,
                &direction_tracker,
                &base_animation,
                &mut gltf_animations,
                &mut animation_players,
                dt,
            );
        } else {
            // Use simple animation system for non-movement animations
            apply_simple_animation(
                entity,
                &mut animation_state,
                &base_animation,
                &mut animation_config,
                &linear_velocity,
                &mut gltf_animations,
                &mut animation_players,
            );
        }
    }
}

/// Apply blended animation with turn support
fn apply_blended_animation(
    _entity: Entity,
    animation_state: &mut AnimationState,
    blend_state: &mut AnimationBlendState,
    direction_tracker: &DirectionTracker,
    base_animation: &OrganismAnimation,
    gltf_animations: &mut GltfAnimations,
    animation_players: &mut Query<&mut AnimationPlayer>,
    delta_time: f32,
) {
    let turn_weight = direction_tracker.get_turn_blend_weight();
    let base_weight = 1.0 - turn_weight;

    // Determine turn weights
    let (turn_left_weight, turn_right_weight) =
        if let Some(turn_anim) = direction_tracker.get_turn_animation() {
            match turn_anim {
                OrganismAnimation::TurnLeft => (turn_weight, 0.0),
                OrganismAnimation::TurnRight => (0.0, turn_weight),
                _ => (0.0, 0.0),
            }
        } else {
            (0.0, 0.0)
        };

    // Update blend weights
    blend_state.update_blend_weights(
        base_weight,
        turn_left_weight,
        turn_right_weight,
        delta_time,
    );

    // Apply to animation player
    if let Ok(mut player) = animation_players.get_mut(gltf_animations.animation_player) {
        // Play base animation
        if let Some(base_handle) =
            gltf_animations.get(base_animation.get_animation_index())
        {
            if !player.is_playing_animation(base_handle) {
                let mut animation = player.start(base_handle);
                animation.repeat();
            }

            if let Some(mut active_animation) = player.animation_mut(base_handle) {
                active_animation.set_weight(blend_state.get_base_weight());
            }
        }

        // Play turn animations if needed
        if turn_left_weight > 0.0 {
            if let Some(turn_left_handle) =
                gltf_animations.get(AnimalAnimationIndex::TURN_LEFT)
            {
                if !player.is_playing_animation(turn_left_handle) {
                    let mut animation = player.start(turn_left_handle);
                    animation.repeat();
                }

                if let Some(mut active_animation) = player.animation_mut(turn_left_handle)
                {
                    active_animation.set_weight(blend_state.get_turn_left_weight());
                }
            }
        }

        if turn_right_weight > 0.0 {
            if let Some(turn_right_handle) =
                gltf_animations.get(AnimalAnimationIndex::TURN_RIGHT)
            {
                if !player.is_playing_animation(turn_right_handle) {
                    let mut animation = player.start(turn_right_handle);
                    animation.repeat();
                }

                if let Some(mut active_animation) =
                    player.animation_mut(turn_right_handle)
                {
                    active_animation.set_weight(blend_state.get_turn_right_weight());
                }
            }
        }
    }

    // Update animation state
    animation_state.current_animation = base_animation.clone();
}

/// Apply simple animation without blending
fn apply_simple_animation(
    _entity: Entity,
    animation_state: &mut AnimationState,
    desired_animation: &OrganismAnimation,
    animation_config: &mut AnimationConfig,
    linear_velocity: &LinearVelocity,
    gltf_animations: &mut GltfAnimations,
    animation_players: &mut Query<&mut AnimationPlayer>,
) {
    // Calculate animation speed
    let animation_speed =
        crate::systems::animals::animation_controller::calculate_animation_speed(
            desired_animation,
            linear_velocity,
            animation_config,
        );

    // Check if we need to change animation
    if should_change_animation(&animation_state, &desired_animation) {
        animation_state.transition_to(desired_animation.clone());
        animation_state.should_loop = desired_animation.should_loop();
        animation_state.speed_multiplier = animation_speed;
    } else {
        // Update speed for current animation
        animation_state.speed_multiplier = animation_speed;
    }

    // Handle idle animation variations
    if matches!(animation_state.current_animation, OrganismAnimation::Idle)
        && animation_config.should_change_idle_animation()
    {
        let idle_variations = [
            OrganismAnimation::Idle,
            OrganismAnimation::Sit,
            OrganismAnimation::Lay,
        ];

        let random_idle = idle_variations
            [rand::thread_rng().gen_range(0..idle_variations.len())]
        .clone();

        animation_state.transition_to(random_idle);
    }

    // Apply to animation player
    if let Ok(mut player) = animation_players.get_mut(gltf_animations.animation_player) {
        let animation_index = desired_animation.get_animation_index();

        if let Some(animation_handle) = gltf_animations.get(animation_index) {
            if !player.is_playing_animation(animation_handle) {
                let mut animation = player.start(animation_handle);
                animation.set_speed(animation_state.speed_multiplier);

                if animation_state.should_loop {
                    animation.repeat();
                }
            } else {
                if let Some(mut active_animation) = player.animation_mut(animation_handle)
                {
                    active_animation.set_speed(animation_state.speed_multiplier);
                    active_animation.set_weight(1.0); // Full weight for simple animations
                }
            }
        }
    }
}

/// System to initialize blending components for organisms
pub fn initialize_blending_components(
    mut commands: Commands,
    query: Query<Entity, (With<GltfAnimations>, Without<DirectionTracker>)>,
    mut animation_graphs: ResMut<Assets<AnimationGraph>>,
) {
    for entity in query.iter() {
        let animation_graph = AnimationGraph::new();
        let handle = animation_graphs.add(animation_graph);

        commands.entity(entity).insert((
            DirectionTracker::default(),
            AnimationBlendState::default().with_animation_graph(handle),
        ));
    }
}
