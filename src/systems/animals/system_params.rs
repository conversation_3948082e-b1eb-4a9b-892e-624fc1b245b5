use crate::resources::animals::AnimalsAssets;
use bevy::ecs::system::SystemParam;
use bevy::prelude::Res;
use bevy_gltf_animation::gltf_scene::GltfSceneRoot;

#[derive(SystemParam)]
pub struct AnimalsAssetSystemParams<'w> {
    animal_assets: Res<'w, AnimalsAssets>,
}

impl<'w> AnimalsAssetSystemParams<'w> {
    pub fn get_model(&self, name: &str, folder: Option<&str>) -> Option<GltfSceneRoot> {
        self.animal_assets.get_model(name, folder)
    }
}
