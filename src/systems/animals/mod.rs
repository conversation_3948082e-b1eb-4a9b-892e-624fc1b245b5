use crate::components::animals::{
    AnimalBaseArmature, AnimalSpeciesModelKey, AnimalSpeciesModelSet, AutoRigColliders, AutoSetupIK, MovementDampingFactor
};
use crate::libraries::ik_fabrik::chain::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use crate::libraries::inverse_kinematics::IkConstraint;
use crate::systems::animals::system_params::AnimalsAssetSystemParams;
use avian3d::prelude::*;
use bevy::prelude::*;
use bevy::render::primitives::Aabb;
use bevy_descendant_collector::{DescendantCollectorTarget, DescendantLoader};
use bevy_gltf_animation::prelude::{GltfAnimations, GltfSceneRoot};

pub mod animation_blending;
pub mod animation_controller;
pub mod bees;
pub mod system_params;

pub fn set_model_for_species(
    mut commands: Commands,
    animals_assets: AnimalsAssetSystemParams,
    query: Query<(Entity, &AnimalSpeciesModelKey), Without<AnimalSpeciesModelSet>>,
) {
    for (entity, species) in query.iter() {
        let Some(model) = animals_assets.get_model(&species.0, None) else {
            log::warn!("Failed to load model for species: {:?}", species.0);
            continue;
        };

        commands
            .entity(entity)
            .insert((AnimalSpeciesModelSet, model));
    }
}

pub fn apply_movement_damping(
    mut query: Query<(&MovementDampingFactor, &mut LinearVelocity)>,
) {
    for (damping_factor, mut linear_velocity) in &mut query {
        linear_velocity.x *= damping_factor.0;
        linear_velocity.z *= damping_factor.0;
    }
}

pub fn on_auto_setup_ik<Armature>(
    mut commands: Commands,
    query: Query<(Entity, &Armature), (With<AutoSetupIK>, With<Armature>)>,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    transforms: Query<&GlobalTransform>,
) where
    Armature: Component + AnimalBaseArmature,
{
    for (entity, armature) in query.iter() {
        // Setup IK for legs
        let target = commands
            .spawn((
                Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                MeshMaterial3d(materials.add(StandardMaterial {
                    base_color: Color::srgb(1.0, 0.0, 0.0),
                    ..default()
                })),
                Name::new("IK Target"),
                Transform::default(),
            ))
            .id();

        let pole_target = commands
            .spawn((
                Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                MeshMaterial3d(materials.add(StandardMaterial {
                    base_color: Color::srgb(0.0, 1.0, 0.0),
                    ..default()
                })),
                Name::new("Pole Target"),
                Transform::default(),
            ))
            .id();

        commands
            .entity(armature.get_toe_l())
            .insert(IkChain::new(3));
        //     .insert(IkConstraint {
        //     chain_length: 3,
        //     iterations: 20,
        //     target,
        //     enabled: true,
        //     // pole_target: Some(pole_target),
        //     pole_angle: 0.0, //-std::f32::consts::FRAC_PI_2,
        //     ..default()
        // });

        // commands.entity(pole_target).insert(ChildOf(entity));
        // commands.entity(target).insert(ChildOf(entity));
        commands.entity(entity).remove::<AutoSetupIK>();
    }
}

pub fn on_added_gltf_scene<Animal, T>(
    mut commands: Commands,
    gltf_scenes: Query<
        (Entity, &Children),
        (With<Animal>, With<GltfSceneRoot>, Without<T>),
    >,
    children_query: Query<&Children>,
    name_query: Query<&Name>,
) where
    Animal: Component,
    T: Component + DescendantLoader,
{
    for (entity, children) in gltf_scenes.iter() {
        if children.is_empty() {
            continue;
        }

        for child in children_query.iter_descendants(entity) {
            if let Ok(name) = name_query.get(child) {
                if name.contains("Root") {
                    commands
                        .entity(entity)
                        .insert(DescendantCollectorTarget::<T>::default());

                    return;
                }
            }
        }
    }
}

// pub fn auto_rig_colliders<T>(
//     mut commands: Commands,
//     children: Query<&Children>,
//     mesh_query: Query<(&Aabb, &Mesh3d)>,
//     auto_rig_query: Query<&T, (With<AutoRigColliders>, Added<T>)>,
//     transforms: Query<&Transform>,
//     meshes: Res<Assets<Mesh>>,
// ) where
//     T: Component + DescendantLoader + AnimalBaseArmature,
// {
//     for armature in auto_rig_query.iter() {
//         let mut half_extents = Vec3A::ZERO;
//
//         if let Ok(children) = children.get(armature.get_mesh()) {
//             for child in children.iter() {
//                 if let Ok((aabb, mesh_3d)) = mesh_query.get(child) {
//                     // if let Some(mesh) = meshes.get(&mesh_3d.0) {
//                     //     if let Some(names) = mesh.morph_target_names() {
//                     //         log::info!("Mesh has morph targets: {:?}", names);
//                     //     }
//                     // }
//
//                     half_extents = aabb.half_extents;
//                     break;
//                 }
//             }
//         }
//
//         if half_extents == Vec3A::ZERO {
//             continue;
//         }
//
//         // Auto rotate by 180 degrees on the Y axis
//         if let Ok(base_transform) = transforms.get(armature.get_base()) {
//             commands.entity(armature.get_base()).insert(
//                 base_transform.with_rotation(Quat::from_rotation_y(std::f32::consts::PI)),
//             );
//         }
//
//         commands
//             .entity(armature.get_body())
//             .insert(Collider::cuboid(
//                 half_extents.x * 4.,
//                 half_extents.y * 4.,
//                 half_extents.z * 4.,
//             ));
//     }
// }
