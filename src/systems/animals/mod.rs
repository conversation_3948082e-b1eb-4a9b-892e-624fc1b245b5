use crate::components::animals::{
    AnimalBaseArmature, AnimalSpeciesModelKey, AnimalSpeciesModelSet, AutoRigColliders, AutoSetupIK, MovementDampingFactor
};
use crate::libraries::ik_fabrik::chain::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use crate::libraries::inverse_kinematics::IkConstraint;
use crate::systems::animals::system_params::AnimalsAssetSystemParams;
use crate::systems::environment::system_params::TerrainSystemParams;
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::*;
use bevy::prelude::*;
use bevy::render::primitives::Aabb;
use bevy_descendant_collector::{DescendantCollectorTarget, DescendantLoader};
use bevy_gltf_animation::prelude::{GltfAnimations, GltfSceneRoot};

pub mod animation_blending;
pub mod animation_controller;
pub mod bees;
pub mod system_params;

/// Component to mark an IK target for foot placement
#[derive(Component, Reflect, Debug)]
#[reflect(Component)]
pub struct FootIkTarget {
    /// Which foot this target belongs to
    pub foot_type: FootType,
    /// The entity this target is following (the foot/toe entity)
    pub foot_entity: Entity,
    /// Offset from the foot position when placing on ground
    pub ground_offset: Vec3,
    /// Maximum distance the foot can reach
    pub max_reach: f32,
    /// How smoothly the target moves (0.0 = instant, 1.0 = very smooth)
    pub smoothing: f32,
}

/// Identifies which foot this is for
#[derive(Debug, Clone, Copy, PartialEq, Eq, Reflect)]
pub enum FootType {
    LeftFront,
    RightFront,
    LeftBack,
    RightBack,
}

/// Component for animals that have foot IK enabled
#[derive(Component, Reflect, Debug)]
#[reflect(Component)]
pub struct FootIkController {
    /// IK targets for each foot
    pub foot_targets: Vec<Entity>,
    /// How far ahead to place feet when walking
    pub stride_length: f32,
    /// How high to lift feet when stepping
    pub step_height: f32,
    /// Speed of foot placement
    pub placement_speed: f32,
}

pub fn set_model_for_species(
    mut commands: Commands,
    animals_assets: AnimalsAssetSystemParams,
    query: Query<(Entity, &AnimalSpeciesModelKey), Without<AnimalSpeciesModelSet>>,
) {
    for (entity, species) in query.iter() {
        let Some(model) = animals_assets.get_model(&species.0, None) else {
            log::warn!("Failed to load model for species: {:?}", species.0);
            continue;
        };

        commands
            .entity(entity)
            .insert((AnimalSpeciesModelSet, model));
    }
}

pub fn apply_movement_damping(
    mut query: Query<(&MovementDampingFactor, &mut LinearVelocity)>,
) {
    for (damping_factor, mut linear_velocity) in &mut query {
        linear_velocity.x *= damping_factor.0;
        linear_velocity.z *= damping_factor.0;
    }
}

pub fn on_auto_setup_ik<Armature>(
    mut commands: Commands,
    query: Query<(Entity, &Armature), (With<AutoSetupIK>, With<Armature>)>,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    transforms: Query<&GlobalTransform>,
) where
    Armature: Component + AnimalBaseArmature,
{
    for (entity, armature) in query.iter() {
        let mut foot_targets = Vec::new();

        // Setup IK for left foot
        let left_foot_target = commands
            .spawn((
                Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(7, 7))),
                MeshMaterial3d(materials.add(StandardMaterial {
                    base_color: Color::srgb(1.0, 0.0, 0.0),
                    ..default()
                })),
                Name::new("Left Foot IK Target"),
                Transform::default(),
                FootIkTarget {
                    foot_type: FootType::LeftBack,
                    foot_entity: armature.get_toe_l(),
                    ground_offset: Vec3::new(0.0, 0.05, 0.0), // Slightly above ground
                    max_reach: 2.0,
                    smoothing: 0.8,
                },
            ))
            .id();

        // Setup IK chain for left foot
        let mut left_ik_chain = IkChain::new(4);
        left_ik_chain.target = Vec3::ZERO; // Will be updated by foot placement system

        commands
            .entity(armature.get_toe_l())
            .insert(left_ik_chain);

        // Make the target a child of the animal
        commands.entity(left_foot_target).insert(ChildOf(armature.get_toe_l()));
        foot_targets.push(left_foot_target);

        // Add the foot IK controller to the animal
        commands.entity(entity).insert(FootIkController {
            foot_targets,
            stride_length: 1.0,
            step_height: 0.3,
            placement_speed: 5.0,
        });

        commands.entity(entity).remove::<AutoSetupIK>();
    }
}

/// System to update foot IK targets based on ground detection and animal movement
pub fn update_foot_ik_targets(
    mut ik_target_query: Query<(&mut Transform, &FootIkTarget)>,
    mut ik_chain_query: Query<&mut IkChain>,
    foot_controller_query: Query<(&FootIkController, &GlobalTransform, &LinearVelocity)>,
    foot_transforms: Query<&GlobalTransform>,
    terrain_system: TerrainSystemParams,
    spatial_query: SpatialQuery,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (controller, animal_transform, velocity) in foot_controller_query.iter() {
        for &target_entity in &controller.foot_targets {
            if let Ok((mut target_transform, foot_target)) = ik_target_query.get_mut(target_entity) {
                // Get the current foot position
                if let Ok(foot_global_transform) = foot_transforms.get(foot_target.foot_entity) {
                    let foot_pos = foot_global_transform.translation();

                    // Calculate desired foot position based on movement
                    let movement_direction = velocity.0.normalize_or_zero();
                    let stride_offset = movement_direction * controller.stride_length * 0.5;

                    // Target position slightly ahead in movement direction
                    let desired_foot_pos = foot_pos + stride_offset;

                    // Perform ground detection using raycast
                    let ground_height = detect_ground_height(
                        desired_foot_pos,
                        &spatial_query,
                        &terrain_system,
                    );

                    // Calculate final target position on ground
                    let target_pos = Vec3::new(
                        desired_foot_pos.x,
                        ground_height + foot_target.ground_offset.y,
                        desired_foot_pos.z,
                    );

                    // Smoothly move target to new position
                    let current_pos = target_transform.translation;
                    let lerp_factor = (1.0 - foot_target.smoothing) * controller.placement_speed * delta_time.0;
                    let new_pos = current_pos.lerp(target_pos, lerp_factor.min(1.0));

                    target_transform.translation = new_pos;

                    // Update the IK chain target
                    if let Ok(mut ik_chain) = ik_chain_query.get_mut(foot_target.foot_entity) {
                        ik_chain.target = new_pos;
                    }
                }
            }
        }
    }
}

/// Detect ground height at a given position using raycasting and terrain sampling
fn detect_ground_height(
    position: Vec3,
    spatial_query: &SpatialQuery,
    terrain_system: &TerrainSystemParams,
) -> f32 {
    // Start raycast from above the position
    let ray_start = Vec3::new(position.x, position.y + 5.0, position.z);
    let ray_direction = -Dir3::Y;
    let max_distance = 10.0;

    // Perform raycast to find ground
    if let Some(hit) = spatial_query.cast_ray(
        ray_start,
        ray_direction,
        max_distance,
        true,
        &SpatialQueryFilter::default(),
    ) {
        return hit.normal.y
    }

    // Fallback: try to get terrain height from terrain system
    // This is a simplified approach - you might want to implement proper terrain height sampling
    if terrain_system.is_in_bounds(position) {
        // For now, return a default ground level
        // In a full implementation, you'd sample the terrain height map
        return 0.0;
    }

    // Final fallback
    position.y
}

pub fn on_added_gltf_scene<Animal, T>(
    mut commands: Commands,
    gltf_scenes: Query<
        (Entity, &Children),
        (With<Animal>, With<GltfSceneRoot>, Without<T>),
    >,
    children_query: Query<&Children>,
    name_query: Query<&Name>,
) where
    Animal: Component,
    T: Component + DescendantLoader,
{
    for (entity, children) in gltf_scenes.iter() {
        if children.is_empty() {
            continue;
        }

        for child in children_query.iter_descendants(entity) {
            if let Ok(name) = name_query.get(child) {
                if name.contains("Root") {
                    commands
                        .entity(entity)
                        .insert(DescendantCollectorTarget::<T>::default());

                    return;
                }
            }
        }
    }
}

// pub fn auto_rig_colliders<T>(
//     mut commands: Commands,
//     children: Query<&Children>,
//     mesh_query: Query<(&Aabb, &Mesh3d)>,
//     auto_rig_query: Query<&T, (With<AutoRigColliders>, Added<T>)>,
//     transforms: Query<&Transform>,
//     meshes: Res<Assets<Mesh>>,
// ) where
//     T: Component + DescendantLoader + AnimalBaseArmature,
// {
//     for armature in auto_rig_query.iter() {
//         let mut half_extents = Vec3A::ZERO;
//
//         if let Ok(children) = children.get(armature.get_mesh()) {
//             for child in children.iter() {
//                 if let Ok((aabb, mesh_3d)) = mesh_query.get(child) {
//                     // if let Some(mesh) = meshes.get(&mesh_3d.0) {
//                     //     if let Some(names) = mesh.morph_target_names() {
//                     //         log::info!("Mesh has morph targets: {:?}", names);
//                     //     }
//                     // }
//
//                     half_extents = aabb.half_extents;
//                     break;
//                 }
//             }
//         }
//
//         if half_extents == Vec3A::ZERO {
//             continue;
//         }
//
//         // Auto rotate by 180 degrees on the Y axis
//         if let Ok(base_transform) = transforms.get(armature.get_base()) {
//             commands.entity(armature.get_base()).insert(
//                 base_transform.with_rotation(Quat::from_rotation_y(std::f32::consts::PI)),
//             );
//         }
//
//         commands
//             .entity(armature.get_body())
//             .insert(Collider::cuboid(
//                 half_extents.x * 4.,
//                 half_extents.y * 4.,
//                 half_extents.z * 4.,
//             ));
//     }
// }
