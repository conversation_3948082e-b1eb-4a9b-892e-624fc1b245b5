use crate::components::animals::{
    AnimalBaseArmature, AutoRigColliders, MovementDampingFactor
};
use avian3d::prelude::*;
use bevy::prelude::*;
use bevy::render::primitives::Aabb;
use bevy_descendant_collector::{DescendantCollectorTarget, DescendantLoader};
use bevy_gltf_animation::prelude::{GltfAnimations, GltfSceneRoot};

pub mod animation_controller;
pub mod animation_blending;
pub mod bees;
pub mod system_params;

pub fn apply_movement_damping(
    mut query: Query<(&MovementDampingFactor, &mut LinearVelocity)>,
) {
    for (damping_factor, mut linear_velocity) in &mut query {
        linear_velocity.x *= damping_factor.0;
        linear_velocity.z *= damping_factor.0;
    }
}

pub fn on_added_gltf_scene<T>(
    mut commands: Commands,
    gltf_scenes: Query<
        (Entity, &Children),
        (With<GltfSceneRoot>, With<GltfAnimations>, Without<T>),
    >,
) where
    T: Component + DescendantLoader,
{
    for (entity, children) in gltf_scenes.iter() {
        if children.is_empty() {
            continue;
        }

        commands
            .entity(entity)
            .insert(DescendantCollectorTarget::<T>::default());
    }
}

pub fn auto_rig_colliders<T>(
    mut commands: Commands,
    children: Query<&Children>,
    mesh_query: Query<&Aabb>,
    auto_rig_query: Query<(&T), (With<AutoRigColliders>, Added<T>)>,
    transforms: Query<&Transform>,
) where
    T: Component + DescendantLoader + AnimalBaseArmature,
{
    for armature in auto_rig_query.iter() {
        let mut half_extents = Vec3A::ZERO;

        if let Ok(children) = children.get(armature.get_mesh()) {
            for child in children.iter() {
                if let Ok(aabb) = mesh_query.get(child) {
                    half_extents = aabb.half_extents;
                    break;
                }
            }
        }

        if half_extents == Vec3A::ZERO {
            continue;
        }

        // Auto rotate by 180 degrees on the Y axis
        if let Ok(base_transform) = transforms.get(armature.get_base()) {
            commands.entity(armature.get_base()).insert(
                base_transform.with_rotation(Quat::from_rotation_y(std::f32::consts::PI)),
            );
        }

        commands
            .entity(armature.get_body())
            .insert(Collider::cuboid(
                half_extents.x * 4.,
                half_extents.y * 4.,
                half_extents.z * 4.,
            ));
    }
}
