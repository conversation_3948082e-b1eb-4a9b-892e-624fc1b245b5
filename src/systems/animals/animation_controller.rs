use avian3d::prelude::LinearVelocity;
use bevy::prelude::*;
use bevy_gltf_animation::prelude::GltfAnimations;

use crate::behaviors::common::EatFoodBehavior;
use crate::components::animals::animation_state::{
    AnimationConfig, AnimationState, OrganismAnimation
};
use crate::components::animals::InWater;
use crate::components::environment::{Terrain, TerrainPod};
use crate::components::lifecycle::{CurrentTerrainPod, Dying};

/// Determine what animation should be playing based on organism state
pub fn determine_base_animation(
    linear_velocity: &LinearVelocity,
    animation_config: &AnimationConfig,
    dying: Option<&Dying>,
    eating_behavior: Option<&EatFoodBehavior>,
    in_water: Option<&InWater>,
    _current_pod: Option<&CurrentTerrainPod>,
    _terrain_query: &Query<&Terrain>,
    _terrain_pod_query: &Query<&TerrainPod>,
) -> OrganismAnimation {
    // Death has the highest priority
    if dying.is_some() {
        return OrganismAnimation::Death;
    }

    // Eating has the second-highest priority
    if eating_behavior.is_some() {
        return OrganismAnimation::Eat;
    }

    // Check if organism is in water (swimming)
    if in_water.is_some() {
        return OrganismAnimation::Swim;
    }

    // Movement-based animations
    let speed = linear_velocity.0.length();

    if speed <= animation_config.idle_threshold {
        OrganismAnimation::Idle
    } else if speed >= animation_config.run_threshold {
        OrganismAnimation::Run
    } else {
        OrganismAnimation::Walk
    }
}

/// Calculate the appropriate animation speed based on velocity and animation type
pub fn calculate_animation_speed(
    animation: &OrganismAnimation,
    linear_velocity: &LinearVelocity,
    animation_config: &AnimationConfig,
) -> f32 {
    let speed = linear_velocity.0.length();

    match animation {
        OrganismAnimation::Walk => {
            // Scale walk animation speed based on actual velocity
            let normalized_speed = (speed / animation_config.run_threshold).min(1.0);
            animation_config.walk_speed_multiplier * (0.5 + normalized_speed * 0.5)
        }
        OrganismAnimation::Run => {
            // Scale run animation speed based on velocity above run threshold
            let excess_speed = (speed - animation_config.run_threshold).max(0.0);
            animation_config.run_speed_multiplier * (1.0 + excess_speed * 0.1)
        }
        OrganismAnimation::Swim => {
            // Swimming speed based on velocity
            let normalized_speed = (speed / animation_config.run_threshold).min(1.0);
            0.8 + normalized_speed * 0.4
        }
        _ => 1.0, // Default speed for other animations
    }
}

/// Check if the animation should be changed
pub fn should_change_animation(
    current_state: &AnimationState,
    desired_animation: &OrganismAnimation,
) -> bool {
    // Always change if the desired animation has higher priority
    if desired_animation.get_priority() > current_state.current_animation.get_priority() {
        return true;
    }

    // Change if it's a different animation of the same or lower priority
    if desired_animation != &current_state.current_animation {
        return true;
    }

    false
}

/// System to initialize animation components for organisms that don't have them
pub fn initialize_animation_components(
    mut commands: Commands,
    query: Query<Entity, (With<GltfAnimations>, Without<AnimationState>)>,
) {
    for entity in query.iter() {
        commands
            .entity(entity)
            .insert((AnimationState::default(), AnimationConfig::default()));
    }
}
