use avian3d::prelude::LinearVelocity;
use bevy::prelude::*;
use bevy_gltf_animation::prelude::GltfAnimations;
use rand::Rng;

use crate::behaviors::common::EatFoodBehavior;
use crate::components::animals::animation_state::{
    AnimationConfig, AnimationState, OrganismAnimation
};
use crate::components::animals::InWater;
use crate::components::environment::{Terrain, TerrainPod, TerrainType};
use crate::components::lifecycle::{CurrentTerrainPod, Dying};
use crate::resources::WorldSimulationDeltaTime;

/// Determine what animation should be playing based on organism state
pub fn determine_base_animation(
    linear_velocity: &LinearVelocity,
    animation_config: &AnimationConfig,
    dying: Option<&Dying>,
    eating_behavior: Option<&EatFoodBehavior>,
    in_water: Option<&InWater>,
    _current_pod: Option<&CurrentTerrainPod>,
    _terrain_query: &Query<&Terrain>,
    _terrain_pod_query: &Query<&TerrainPod>,
) -> OrganismAnimation {
    // Death has the highest priority
    if dying.is_some() {
        return OrganismAnimation::Death;
    }

    // Eating has the second-highest priority
    if eating_behavior.is_some() {
        return OrganismAnimation::Eat;
    }

    // Check if organism is in water (swimming)
    if in_water.is_some() {
        return OrganismAnimation::Swim;
    }

    // Movement-based animations
    let speed = linear_velocity.0.length();

    if speed < animation_config.idle_threshold {
        OrganismAnimation::Idle
    } else if speed >= animation_config.run_threshold {
        OrganismAnimation::Run
    } else {
        OrganismAnimation::Walk
    }
}

/// Calculate the appropriate animation speed based on velocity and animation type
pub fn calculate_animation_speed(
    animation: &OrganismAnimation,
    linear_velocity: &LinearVelocity,
    animation_config: &AnimationConfig,
) -> f32 {
    let speed = linear_velocity.0.length();

    match animation {
        OrganismAnimation::Walk => {
            // Scale walk animation speed based on actual velocity
            let normalized_speed = (speed / animation_config.run_threshold).min(1.0);
            animation_config.walk_speed_multiplier * (0.5 + normalized_speed * 0.5)
        }
        OrganismAnimation::Run => {
            // Scale run animation speed based on velocity above run threshold
            let excess_speed = (speed - animation_config.run_threshold).max(0.0);
            animation_config.run_speed_multiplier * (1.0 + excess_speed * 0.1)
        }
        OrganismAnimation::Swim => {
            // Swimming speed based on velocity
            let normalized_speed = (speed / animation_config.run_threshold).min(1.0);
            0.8 + normalized_speed * 0.4
        }
        _ => 1.0, // Default speed for other animations
    }
}

/// Check if the animation should be changed
pub fn should_change_animation(
    current_state: &AnimationState,
    desired_animation: &OrganismAnimation,
) -> bool {
    // Always change if the desired animation has higher priority
    if desired_animation.get_priority() > current_state.current_animation.get_priority() {
        return true;
    }

    // Change if it's a different animation of the same or lower priority
    if desired_animation != &current_state.current_animation {
        return true;
    }

    false
}

/// Apply the animation state to the actual animation player
fn apply_animation_to_player(
    entity: Entity,
    animation_state: &AnimationState,
    gltf_animations: &mut GltfAnimations,
    animation_players: &mut Query<&mut AnimationPlayer>,
    _commands: &mut Commands,
) {
    let Ok(mut player) = animation_players.get_mut(gltf_animations.animation_player)
    else {
        warn!("No animation player found for entity {:?}", entity);
        return;
    };

    let animation_index = animation_state.current_animation.get_animation_index();

    let Some(animation_handle) = gltf_animations.get(animation_index) else {
        warn!("Animation index {} not found for entity {:?}", animation_index, entity);
        return;
    };

    // Check if we need to start a new animation
    if !player.is_playing_animation(animation_handle) {
        let mut animation = player.start(animation_handle);

        // Configure the animation
        animation.set_speed(animation_state.speed_multiplier);

        if animation_state.should_loop {
            animation.repeat();
        }
    } else {
        // Update the speed of the currently playing animation
        if let Some(mut active_animation) = player.animation_mut(animation_handle) {
            active_animation.set_speed(animation_state.speed_multiplier);
        }
    }
}

/// System to initialize animation components for organisms that don't have them
pub fn initialize_animation_components(
    mut commands: Commands,
    query: Query<Entity, (With<GltfAnimations>, Without<AnimationState>)>,
) {
    for entity in query.iter() {
        commands
            .entity(entity)
            .insert((AnimationState::default(), AnimationConfig::default()));
    }
}
