use bevy::prelude::*;
use super::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>};

/// Different gait patterns for quadrupeds
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq)]
pub enum GaitPattern {
    Walk,
    Trot,
    Canter,
    <PERSON>allo<PERSON>,
}

impl GaitPattern {
    /// Get the appropriate gait pattern based on speed
    pub fn from_speed(speed: f32) -> Self {
        match speed {
            s if s < 1.0 => GaitPattern::Walk,
            s if s < 3.0 => GaitPattern::Trot,
            s if s < 6.0 => GaitPattern::Canter,
            _ => GaitPattern::Gallop,
        }
    }
}

/// Get the phase offset for a specific leg in the gait cycle
pub fn get_leg_phase_offset(leg_type: LegType, current_phase: f32) -> f32 {
    let base_offset = match leg_type {
        LegType::FrontLeft => 0.0,
        LegType::BackRight => 0.25,  // Diagonal pair with front left
        LegType::FrontRight => 0.5,
        LegType::BackLeft => 0.75,   // Diagonal pair with front right
    };
    
    (current_phase + base_offset) % 1.0
}

/// Calculate stride offset for a leg based on gait pattern
pub fn calculate_stride_offset(
    leg_type: LegType,
    leg_phase: f32,
    movement_direction: Vec3,
    step_length: f32,
) -> Vec3 {
    // Calculate forward/backward offset based on gait phase
    let stride_progress = (leg_phase * 2.0 * std::f32::consts::PI).sin();
    let forward_offset = stride_progress * step_length * 0.5;
    
    // Calculate lateral offset for stability
    let lateral_offset = match leg_type {
        LegType::FrontLeft | LegType::BackLeft => -0.1,
        LegType::FrontRight | LegType::BackRight => 0.1,
    };
    
    let forward_dir = movement_direction;
    let right_dir = Vec3::new(-forward_dir.z, 0.0, forward_dir.x);
    
    forward_dir * forward_offset + right_dir * lateral_offset
}

/// Determine if a foot should start stepping
pub fn should_foot_step(
    foot_chain: &FootIkChain,
    target_pos: Vec3,
    gait: &GaitController,
) -> bool {
    let distance_to_target = foot_chain.current_target.distance(target_pos);
    let max_reach = foot_chain.max_reach * 0.8; // Step before reaching max extension
    
    // Step if target is too far away
    if distance_to_target > max_reach {
        return true;
    }
    
    // Step if we've been planted for too long and need to move
    if let super::FootState::Planted { time_planted } = foot_chain.foot_state {
        if time_planted > gait.step_duration * 2.0 && gait.is_moving {
            return true;
        }
    }
    
    false
}

/// Calculate body sway and tilt based on foot positions
pub fn calculate_body_adjustment(
    leg_positions: &[Vec3; 4],
    body_height: f32,
) -> (Vec3, Quat) {
    // Calculate center of support
    let center_of_support = leg_positions.iter().sum::<Vec3>() / 4.0;
    
    // Calculate body position (slightly above center of support)
    let body_position = Vec3::new(
        center_of_support.x,
        center_of_support.y + body_height,
        center_of_support.z,
    );
    
    // Calculate body tilt based on terrain slope
    let front_avg = (leg_positions[0] + leg_positions[1]) / 2.0;
    let back_avg = (leg_positions[2] + leg_positions[3]) / 2.0;
    let left_avg = (leg_positions[0] + leg_positions[2]) / 2.0;
    let right_avg = (leg_positions[1] + leg_positions[3]) / 2.0;
    
    // Calculate pitch (front-back tilt)
    let pitch_angle = (front_avg.y - back_avg.y).atan2(2.0); // Assuming 2.0 unit body length
    
    // Calculate roll (left-right tilt)
    let roll_angle = (right_avg.y - left_avg.y).atan2(1.0); // Assuming 1.0 unit body width
    
    let body_rotation = Quat::from_euler(EulerRot::XYZ, pitch_angle, 0.0, roll_angle);
    
    (body_position, body_rotation)
}

/// Advanced gait timing for different speeds
pub fn get_advanced_gait_timing(speed: f32, leg_type: LegType) -> (f32, f32) {
    let gait_pattern = GaitPattern::from_speed(speed);
    
    match gait_pattern {
        GaitPattern::Walk => {
            // Walk: each leg moves independently, 25% phase offset
            let phase_offset = match leg_type {
                LegType::FrontLeft => 0.0,
                LegType::BackLeft => 0.25,
                LegType::BackRight => 0.5,
                LegType::FrontRight => 0.75,
            };
            (phase_offset, 0.3) // 30% of cycle in air
        }
        
        GaitPattern::Trot => {
            // Trot: diagonal pairs move together
            let phase_offset = match leg_type {
                LegType::FrontLeft | LegType::BackRight => 0.0,
                LegType::FrontRight | LegType::BackLeft => 0.5,
            };
            (phase_offset, 0.4) // 40% of cycle in air
        }
        
        GaitPattern::Canter => {
            // Canter: three-beat gait
            let phase_offset = match leg_type {
                LegType::BackLeft => 0.0,
                LegType::BackRight | LegType::FrontLeft => 0.33,
                LegType::FrontRight => 0.66,
            };
            (phase_offset, 0.5) // 50% of cycle in air
        }
        
        GaitPattern::Gallop => {
            // Gallop: four-beat gait with suspension
            let phase_offset = match leg_type {
                LegType::BackLeft => 0.0,
                LegType::BackRight => 0.2,
                LegType::FrontLeft => 0.4,
                LegType::FrontRight => 0.6,
            };
            (phase_offset, 0.6) // 60% of cycle in air
        }
    }
}

/// Calculate foot placement for stability
pub fn calculate_stable_foot_placement(
    leg_type: LegType,
    body_center: Vec3,
    movement_velocity: Vec3,
    step_length: f32,
) -> Vec3 {
    // Base foot position relative to body center
    let base_offset = match leg_type {
        LegType::FrontLeft => Vec3::new(-0.3, 0.0, 0.5),
        LegType::FrontRight => Vec3::new(0.3, 0.0, 0.5),
        LegType::BackLeft => Vec3::new(-0.3, 0.0, -0.5),
        LegType::BackRight => Vec3::new(0.3, 0.0, -0.5),
    };
    
    // Adjust for movement direction and speed
    let movement_adjustment = movement_velocity.normalize_or_zero() * step_length * 0.3;
    
    // Add stability margin
    let stability_adjustment = match leg_type {
        LegType::FrontLeft | LegType::FrontRight => movement_adjustment * 0.5,
        LegType::BackLeft | LegType::BackRight => -movement_adjustment * 0.3,
    };
    
    body_center + base_offset + stability_adjustment
}

/// Calculate the ideal step height based on terrain and speed
pub fn calculate_adaptive_step_height(
    base_step_height: f32,
    terrain_roughness: f32,
    speed: f32,
) -> f32 {
    let speed_factor = (speed / 5.0).min(1.0); // Normalize to 0-1
    let terrain_factor = terrain_roughness.clamp(0.0, 2.0);
    
    base_step_height * (1.0 + speed_factor * 0.5 + terrain_factor * 0.3)
}
