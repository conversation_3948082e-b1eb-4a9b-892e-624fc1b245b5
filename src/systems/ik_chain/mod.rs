use crate::libraries::ik_fabrik::chain::I<PERSON><PERSON><PERSON><PERSON>;
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::prelude::*;
use bevy::prelude::*;

pub mod gait_patterns;
pub mod ground_detection;

/// Identifies which leg this is for a quadruped
#[derive(Debug, Clone, Copy, PartialEq, Eq, Reflect, Component)]
pub enum LegType {
    FrontLeft,
    FrontRight,
    BackLeft,
    BackRight,
}

impl LegType {
    pub fn all() -> [LegType; 4] {
        [
            LegType::FrontLeft,
            LegType::FrontRight,
            LegType::BackLeft,
            LegType::BackRight,
        ]
    }

    pub fn is_front(&self) -> bool {
        matches!(self, LegType::FrontLeft | LegType::FrontRight)
    }

    pub fn is_left(&self) -> bool {
        matches!(self, LegType::FrontLeft | LegType::BackLeft)
    }

    pub fn diagonal_pair(&self) -> LegType {
        match self {
            LegType::FrontLeft => LegType::BackRight,
            LegType::FrontRight => LegType::BackLeft,
            LegType::BackLeft => LegType::FrontRight,
            LegType::BackRight => LegType::FrontLeft,
        }
    }
}

/// State of an individual foot during walking cycle
#[derive(Debug, Clone, Copy, PartialEq, Reflect, Component)]
pub enum FootState {
    /// Foot is on the ground, supporting weight
    Planted { time_planted: f32 },
    /// Foot is lifting off the ground
    Lifting { progress: f32 },
    /// Foot is moving through the air to new position
    Swinging { progress: f32, target_pos: Vec3 },
    /// Foot is placing down at target position
    Placing { progress: f32, target_pos: Vec3 },
}

impl Default for FootState {
    fn default() -> Self {
        FootState::Planted { time_planted: 0.0 }
    }
}

/// IK chain data for a single foot
#[derive(Component, Reflect)]
pub struct FootIkChain {
    pub leg_type: LegType,
    pub foot_entity: Entity,
    pub target_entity: Entity,
    pub current_target: Vec3,
    pub rest_position: Vec3,
    pub step_height: f32,
    pub max_reach: f32,
    pub foot_state: FootState,
}

/// Gait controller for managing walking patterns
#[derive(Component, Reflect)]
pub struct GaitController {
    pub gait_speed: f32,
    pub step_length: f32,
    pub step_height: f32,
    pub step_duration: f32,
    pub body_height: f32,
    pub stability_margin: f32,
    pub current_phase: f32,
    pub is_moving: bool,
}

impl Default for GaitController {
    fn default() -> Self {
        Self {
            gait_speed: 1.0,
            step_length: 0.8,
            step_height: 0.2,
            step_duration: 0.5,
            body_height: 1.0,
            stability_margin: 0.1,
            current_phase: 0.0,
            is_moving: false,
        }
    }
}

/// Main controller for quadruped IK system
#[derive(Component, Reflect)]
pub struct QuadrupedIkController {
    pub legs: [Entity; 4], // [FrontLeft, FrontRight, BackLeft, BackRight]
    pub body_entity: Entity,
    pub is_initialized: bool,
}

/// Marker component to trigger IK setup for quadrupeds
#[derive(Component)]
pub struct SetupQuadrupedIk;

/// System to set up IK chains for quadrupeds
pub fn setup_quadruped_ik_system(
    mut commands: Commands,
    query: Query<
        (Entity, &Children),
        (With<SetupQuadrupedIk>, Without<QuadrupedIkController>),
    >,
    mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
    name_query: Query<&Name>,
    transform_query: Query<&Transform>,
) {
    for (entity, children) in query.iter() {
        // Find foot/toe entities by name
        let mut foot_entities = [Entity::PLACEHOLDER; 4];
        let mut found_feet = 0;

        // Search through all descendants to find foot bones
        find_foot_entities(children, &name_query, &mut foot_entities, &mut found_feet);

        if found_feet < 4 {
            warn!(
                "Could not find all 4 feet for quadruped IK setup on entity {:?}",
                entity
            );
            commands.entity(entity).remove::<SetupQuadrupedIk>();
            continue;
        }

        // Create IK targets and chains for each foot
        let mut leg_entities = [Entity::PLACEHOLDER; 4];

        for (i, &foot_entity) in foot_entities.iter().enumerate() {
            let leg_type = match i {
                0 => LegType::FrontLeft,
                1 => LegType::FrontRight,
                2 => LegType::BackLeft,
                3 => LegType::BackRight,
                _ => unreachable!(),
            };

            // Create IK target sphere
            let target_entity = commands
                .spawn((
                    Mesh3d(meshes.add(Sphere::new(0.05).mesh().uv(8, 8))),
                    MeshMaterial3d(materials.add(StandardMaterial {
                        base_color: Color::srgb(1.0, 0.2, 0.2),
                        ..default()
                    })),
                    Transform::default(),
                    Name::new(format!("{:?} IK Target", leg_type)),
                ))
                .id();

            // Get rest position for this foot
            let rest_position =
                if let Ok(foot_transform) = transform_query.get(foot_entity) {
                    foot_transform.translation
                } else {
                    Vec3::ZERO
                };

            // Create foot IK chain component
            let foot_ik_chain = FootIkChain {
                leg_type,
                foot_entity,
                target_entity,
                current_target: rest_position,
                rest_position,
                step_height: 0.3,
                max_reach: 1.5,
                foot_state: FootState::default(),
            };

            // Create leg entity to hold the IK chain
            let leg_entity = commands
                .spawn((
                    foot_ik_chain,
                    Name::new(format!("{:?} Leg Controller", leg_type)),
                ))
                .id();

            // Set up IK chain on the foot bone
            commands.entity(foot_entity).insert(IkChain::new(3));

            // Make target a child of the main entity
            commands.entity(target_entity).insert(ChildOf(entity));
            commands.entity(leg_entity).insert(ChildOf(entity));

            leg_entities[i] = leg_entity;
        }

        // Add the main quadruped controller
        commands.entity(entity).insert((
            QuadrupedIkController {
                legs: leg_entities,
                body_entity: entity,
                is_initialized: true,
            },
            GaitController::default(),
        ));

        commands.entity(entity).remove::<SetupQuadrupedIk>();
        info!("Successfully set up quadruped IK for entity {:?}", entity);
    }
}

fn find_foot_entities(
    children: &Children,
    name_query: &Query<&Name>,
    foot_entities: &mut [Entity; 4],
    found_feet: &mut usize,
) {
    for child in children.iter() {
        if let Ok(name) = name_query.get(child) {
            let name_str = name.as_str().to_lowercase();

            // Match foot/toe bone names
            if name_str.contains("toe") || name_str.contains("foot") {
                if name_str.contains("left") || name_str.contains(".L") {
                    if name_str.contains("front")
                        || (!name_str.contains("back") && *found_feet < 2)
                    {
                        foot_entities[0] = child; // Front left
                        *found_feet += 1;
                    } else {
                        foot_entities[2] = child; // Back left
                        *found_feet += 1;
                    }
                } else if name_str.contains("right") || name_str.contains(".R") {
                    if name_str.contains("front")
                        || (!name_str.contains("back") && *found_feet < 2)
                    {
                        foot_entities[1] = child; // Front right
                        *found_feet += 1;
                    } else {
                        foot_entities[3] = child; // Back right
                        *found_feet += 1;
                    }
                }
            }
        }
    }
}

/// System to update gait patterns and foot states
pub fn update_gait_system(
    mut gait_query: Query<(&mut GaitController, &LinearVelocity)>,
    mut _foot_query: Query<&mut FootIkChain>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (mut gait, velocity) in gait_query.iter_mut() {
        let speed = velocity.0.length();
        gait.is_moving = speed > 0.1;

        if gait.is_moving {
            gait.current_phase += delta_time.0 * gait.gait_speed;
            if gait.current_phase >= 1.0 {
                gait.current_phase -= 1.0;
            }
        }
    }
}

/// Main system to update quadruped IK
pub fn update_quadruped_ik_system(
    mut ik_query: Query<(
        &QuadrupedIkController,
        &GaitController,
        &GlobalTransform,
        &LinearVelocity,
    )>,
    mut foot_query: Query<&mut FootIkChain>,
    mut ik_chain_query: Query<&mut IkChain>,
    mut target_transforms: Query<&mut Transform, Without<FootIkChain>>,
    terrain_system: TerrainSystemParams,
    spatial_query: SpatialQuery,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (controller, gait, body_transform, velocity) in ik_query.iter_mut() {
        if !controller.is_initialized {
            continue;
        }

        // Update each leg
        for &leg_entity in &controller.legs {
            if let Ok(mut foot_chain) = foot_query.get_mut(leg_entity) {
                update_foot_ik_chain(
                    &mut foot_chain,
                    gait,
                    body_transform,
                    velocity,
                    &mut target_transforms,
                    &mut ik_chain_query,
                    &terrain_system,
                    &spatial_query,
                    delta_time.0,
                );
            }
        }
    }
}

fn update_foot_ik_chain(
    foot_chain: &mut FootIkChain,
    gait: &GaitController,
    body_transform: &GlobalTransform,
    velocity: &LinearVelocity,
    target_transforms: &mut Query<&mut Transform, Without<FootIkChain>>,
    ik_chain_query: &mut Query<&mut IkChain>,
    terrain_system: &TerrainSystemParams,
    spatial_query: &SpatialQuery,
    delta_time: f32,
) {
    // Calculate target position based on gait pattern
    let target_pos = calculate_foot_target_position(
        foot_chain,
        gait,
        body_transform,
        velocity,
        terrain_system,
        spatial_query,
    );

    // Update foot state and target
    update_foot_state(foot_chain, target_pos, gait, delta_time);

    // Apply target to IK chain
    if let Ok(mut target_transform) = target_transforms.get_mut(foot_chain.target_entity)
    {
        target_transform.translation = foot_chain.current_target;
    }

    if let Ok(mut ik_chain) = ik_chain_query.get_mut(foot_chain.foot_entity) {
        ik_chain.target = foot_chain.current_target;
    }
}

fn calculate_foot_target_position(
    foot_chain: &FootIkChain,
    gait: &GaitController,
    body_transform: &GlobalTransform,
    velocity: &LinearVelocity,
    terrain_system: &TerrainSystemParams,
    spatial_query: &SpatialQuery,
) -> Vec3 {
    use crate::systems::ik_chain::gait_patterns::*;

    if !gait.is_moving {
        return foot_chain.rest_position;
    }

    // Calculate base foot position relative to body
    let body_pos = body_transform.translation();
    let movement_dir = velocity.0.normalize_or_zero();

    // Get gait pattern timing for this leg
    let leg_phase = get_leg_phase_offset(foot_chain.leg_type, gait.current_phase);

    // Calculate stride position
    let stride_offset = calculate_stride_offset(
        foot_chain.leg_type,
        leg_phase,
        movement_dir,
        gait.step_length,
    );

    // Calculate base target position
    let base_target = body_pos + foot_chain.rest_position + stride_offset;

    // Sample ground height at target position
    let ground_height = sample_ground_height(base_target, spatial_query, terrain_system);

    Vec3::new(base_target.x, ground_height, base_target.z)
}

fn update_foot_state(
    foot_chain: &mut FootIkChain,
    target_pos: Vec3,
    gait: &GaitController,
    delta_time: f32,
) {
    use crate::systems::ik_chain::gait_patterns::*;

    // let distance_to_target = foot_chain.current_target.distance(target_pos);
    let should_step = should_foot_step(foot_chain, target_pos, gait);
    let mut new_state = foot_chain.foot_state;

    match &mut foot_chain.foot_state {
        FootState::Planted { time_planted } => {
            *time_planted += delta_time;

            if should_step {
                new_state = FootState::Lifting { progress: 0.0 };
            } else {
                foot_chain.current_target = target_pos;
            }
        }

        FootState::Lifting { progress } => {
            *progress += delta_time / (gait.step_duration * 0.2); // 20% of step for lifting

            if *progress >= 1.0 {
                foot_chain.foot_state = FootState::Swinging {
                    progress: 0.0,
                    target_pos,
                };
            } else {
                // Lift foot up
                let lift_height = (*progress * gait.step_height).min(gait.step_height);
                foot_chain.current_target.y = target_pos.y + lift_height;
            }
        }

        FootState::Swinging {
            progress,
            target_pos: swing_target,
        } => {
            *progress += delta_time / (gait.step_duration * 0.6); // 60% of step for swinging

            if *progress >= 1.0 {
                foot_chain.foot_state = FootState::Placing {
                    progress: 0.0,
                    target_pos: *swing_target,
                };
            } else {
                // Arc motion through the air
                let start_pos = foot_chain.current_target;
                let arc_height =
                    gait.step_height * (1.0 - (2.0 * *progress - 1.0).powi(2));

                foot_chain.current_target = start_pos.lerp(*swing_target, *progress);
                foot_chain.current_target.y = swing_target.y + arc_height;
            }
        }

        FootState::Placing {
            progress,
            target_pos: place_target,
        } => {
            *progress += delta_time / (gait.step_duration * 0.2); // 20% of step for placing

            if *progress >= 1.0 {
                new_state = FootState::Planted { time_planted: 0.0 };
                foot_chain.current_target = *place_target;
            } else {
                // Lower foot to ground
                let current_height = foot_chain.current_target.y;
                let target_height = place_target.y;
                foot_chain.current_target.y =
                    current_height.lerp(target_height, *progress);
            }
        }
    }

    foot_chain.foot_state = new_state;
}

fn sample_ground_height(
    position: Vec3,
    spatial_query: &SpatialQuery,
    _terrain_system: &TerrainSystemParams,
) -> f32 {
    // Raycast downward to find ground
    let ray_start = Vec3::new(position.x, position.y + 2.0, position.z);
    let ray_direction = -Dir3::Y;

    if let Some(hit) = spatial_query.cast_ray(
        ray_start,
        ray_direction,
        5.0,
        true,
        &SpatialQueryFilter::default(),
    ) {
        return hit.normal.y;
    }

    // Fallback to terrain height or current position
    position.y
}
