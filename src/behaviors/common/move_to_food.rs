use crate::behaviors::common::food_detection::FoodSourceTarget;
use crate::behaviors::common::move_to_target::MoveToTargetBehavior;
use crate::components::lifecycle::Hunger;
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::*;
use bevy_behave::prelude::*;

/// Behavior component for moving to a food source
#[derive(Reflect, Component, Clone)]
#[reflect(Component)]
pub struct MoveToFoodBehavior {
    /// Maximum speed for movement
    pub max_speed: f32,
    /// Distance at which to start slowing down
    pub slowing_radius: f32,
    /// Distance at which to consider arrival
    pub eating_distance: f32,
    /// Whether this behavior is used for common behavior trees
    pub not_used_for_common_behavior: bool,
    pub max_turn_speed: f32,
    pub turn_acceleration: f32,
}

impl MoveToTargetBehavior for MoveToFoodBehavior {
    fn get_max_turn_speed(&self) -> f32 {
        self.max_turn_speed
    }

    fn get_turn_acceleration(&self) -> f32 {
        self.turn_acceleration
    }

    fn get_target_distance(&self) -> Option<f32> {
        Some(self.eating_distance)
    }
}

impl Default for MoveToFoodBehavior {
    fn default() -> Self {
        Self {
            max_speed: 2.0,
            slowing_radius: 1.0,
            eating_distance: 0.5,
            not_used_for_common_behavior: false,
            max_turn_speed: 2.0,
            turn_acceleration: 4.0,
        }
    }
}

/// System to move an organism toward a food source
pub fn move_to_food_behavior_system(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &MoveToFoodBehavior)>,
    mut organism_query: Query<(
        &mut Transform,
        &mut LinearVelocity,
        &mut AngularVelocity,
        Option<&FoodSourceTarget>,
    )>,
    food_query: Query<&GlobalTransform>,
) {
    for (ctx, behavior) in tasks.iter_mut() {
        if behavior.not_used_for_common_behavior {
            continue;
        }

        let target_entity = ctx.target_entity();

        // Get organism components
        if let Ok((
            mut transform,
            mut linear_velocity,
            mut angular_velocity,
            food_target,
        )) = organism_query.get_mut(target_entity)
        {
            // Check if we have a food target
            if let Some(food_target) = food_target {
                // Get food source transform
                if let Ok(food_transform) = food_query.get(food_target.0) {
                    // Calculate direction and distance to food
                    let direction = food_transform.translation() - transform.translation;
                    let distance = direction.length();

                    // Check if we've arrived at the food
                    if distance <= behavior.eating_distance {
                        // We've arrived, stop moving
                        linear_velocity.0 = Vec3::ZERO;
                        angular_velocity.0 = Vec3::ZERO;
                        commands.trigger(ctx.success());
                        continue;
                    }

                    // Calculate desired velocity
                    let desired_velocity = if distance > behavior.slowing_radius {
                        // Move at max speed
                        direction.normalize() * behavior.max_speed
                    } else {
                        // Slow down as we approach
                        direction.normalize()
                            * behavior.max_speed
                            * (distance / behavior.slowing_radius)
                    };

                    // Set velocity
                    linear_velocity.0 = desired_velocity;

                    // Calculate rotation to face the food
                    let forward = Vec3::new(0.0, 0.0, 1.0);
                    let target_direction = direction.normalize();

                    if target_direction.length_squared() > 0.001 {
                        let rotation = Quat::from_rotation_arc(forward, target_direction);
                        transform.rotation = rotation;
                    }

                    continue;
                }
            }

            // If we get here, we don't have a valid food target
            commands.trigger(ctx.failure());
        } else {
            // If we can't get the required components
            commands.trigger(ctx.failure());
        }
    }
}
