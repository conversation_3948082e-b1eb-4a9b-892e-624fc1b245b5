use crate::behaviors::common::move_to_target::MoveToTargetBehavior;
use crate::behaviors::common::water_detection::WaterSourceTarget;
use crate::components::lifecycle::Thirst;
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::*;
use bevy_behave::prelude::*;

/// Behavior component for moving to a water source
#[derive(Reflect, Component, Clone)]
#[reflect(Component)]
pub struct MoveToWaterBehavior {
    /// How close the organism needs to be to drink from the water
    pub drinking_distance: f32,
    /// Maximum speed when moving to water
    pub max_speed: f32,
    /// Distance at which to start slowing down
    pub slowing_radius: f32,
    /// Not used for common behavior, but organism-specific
    pub not_used_for_common_behavior: bool,
    pub max_turn_speed: f32,
    pub turn_acceleration: f32,
}

impl MoveToTargetBehavior for MoveToWaterBehavior {
    fn get_max_turn_speed(&self) -> f32 {
        self.max_turn_speed
    }

    fn get_turn_acceleration(&self) -> f32 {
        self.turn_acceleration
    }

    fn get_target_distance(&self) -> Option<f32> {
        Some(self.drinking_distance)
    }
}

impl Default for MoveToWaterBehavior {
    fn default() -> Self {
        Self {
            drinking_distance: 0.3,
            max_speed: 1.0,
            slowing_radius: 2.0,
            not_used_for_common_behavior: false,
            max_turn_speed: 2.0,
            turn_acceleration: 4.0,
        }
    }
}

/// System to handle moving to a water source
pub fn move_to_water_behavior_system(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &MoveToWaterBehavior)>,
    mut organism_query: Query<
        (&mut Transform, &mut LinearVelocity, &mut AngularVelocity, &WaterSourceTarget),
        With<Thirst>,
    >,
    water_query: Query<&GlobalTransform>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (ctx, behavior) in tasks.iter_mut() {
        if behavior.not_used_for_common_behavior {
            continue;
        }

        let target_entity = ctx.target_entity();

        // Get organism components
        if let Ok((
            mut transform,
            mut linear_velocity,
            mut angular_velocity,
            water_target,
        )) = organism_query.get_mut(target_entity)
        {
            // Get water source transform
            if let Ok(water_transform) = water_query.get(water_target.0) {
                let water_pos = water_transform.translation();
                let organism_pos = transform.translation;

                // Calculate distance to water
                let distance = organism_pos.distance(water_pos);

                // If we're close enough to drink, succeed
                if distance <= behavior.drinking_distance {
                    // Stop moving
                    linear_velocity.0 = Vec3::ZERO;
                    angular_velocity.0 = Vec3::ZERO;

                    commands.trigger(ctx.success());
                    continue;
                }

                // Calculate direction to water
                let direction = (water_pos - organism_pos).normalize();

                // Calculate speed based on distance (slow down as we get closer)
                let speed = if distance < behavior.slowing_radius {
                    behavior.max_speed * (distance / behavior.slowing_radius)
                } else {
                    behavior.max_speed
                };

                // Set velocity
                linear_velocity.0 = direction * speed;

                // Calculate rotation to face the water
                let target_rotation = Quat::from_rotation_arc(Vec3::Z, direction);
                transform.rotation = transform
                    .rotation
                    .slerp(target_rotation, delta_time.0 * 5.0);
            } else {
                log::info!("Water source no longer exists");
                // Water source no longer exists
                commands.entity(target_entity).remove::<WaterSourceTarget>();
                commands.trigger(ctx.failure());
            }
        } else {
            log::info!("Missing required components for MoveToWaterBehavior");
            // Missing required components
            commands.trigger(ctx.failure());
        }
    }
}
