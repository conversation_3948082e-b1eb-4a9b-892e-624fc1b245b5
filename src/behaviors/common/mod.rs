pub mod common_idle_behavior;
pub mod common_search_randomly;
mod drink_water;
mod eat_food;
mod food_detection;
mod food_seeking;
mod move_to_food;
pub mod move_to_target;
mod move_to_water;
mod water_detection;

use crate::behaviors::common::common_search_randomly::{
    common_search_randomly_behavior, CommonSearchRandomlyBehavior
};
use crate::behaviors::common::move_to_target::move_to_target;
use crate::behaviors::neural::needs_behaviors::{
    NeuralNeedFood, NeuralNeedRest, NeuralNeedWater
};
use crate::components::animals::AnimalMovementBehavior;
use crate::components::lifecycle::{Hunger, Thirst};
use crate::plugins::animals::AnimalsPluginState;
use bevy::prelude::*;
use bevy::reflect::GetTypeRegistration;
use bevy_behave::prelude::*;
pub use drink_water::DrinkWaterBehavior;
pub use eat_food::EatFoodBehavior;
pub use food_detection::{needs_food, DetectFoodBehavior, FoodSourceTarget};
pub use food_seeking::{create_food_seeking_behavior, FoodSeekingBehaviorTreeParams};
pub use move_to_food::MoveToFoodBehavior;
pub use move_to_water::MoveToWaterBehavior;
use std::marker::PhantomData;
pub use water_detection::{needs_water, DetectWaterBehavior, WaterSourceTarget};

pub struct BaseBehaviorsPlugin;

impl Plugin for BaseBehaviorsPlugin {
    fn build(&self, app: &mut App) {
        app.register_type::<WaterSourceTarget>()
            .register_type::<MoveToWaterBehavior>()
            .register_type::<DetectWaterBehavior>()
            .register_type::<FoodSourceTarget>()
            .register_type::<MoveToFoodBehavior>()
            .register_type::<DetectFoodBehavior>()
            .register_type::<CommonSearchRandomlyBehavior>()
            .add_observer(needs_water)
            .add_observer(needs_food)
            .add_systems(
                FixedUpdate,
                (
                    // Water-related systems
                    water_detection::detect_water_behavior_system,
                    // move_to_water::move_to_water_behavior_system,
                    drink_water::drink_water_behavior_system,
                    // Food-related systems
                    food_detection::detect_food_behavior_system,
                    // move_to_food::move_to_food_behavior_system,
                    eat_food::eat_food_behavior_system,
                    common_idle_behavior::common_idle_behavior,
                ),
            );
    }
}

#[derive(Default)]
pub struct CommonBehaviorsPlugin<T: Component + AnimalMovementBehavior> {
    _marker: PhantomData<T>,
}

impl<
        T: Component<Mutability = bevy::ecs::component::Mutable> + AnimalMovementBehavior,
    > Plugin for CommonBehaviorsPlugin<T>
{
    fn build(&self, app: &mut App) {
        app.add_systems(
            FixedUpdate,
            (
                common_search_randomly_behavior::<T>,
                move_to_target::<T, MoveToFoodBehavior, FoodSourceTarget, Hunger>,
                move_to_target::<T, MoveToWaterBehavior, WaterSourceTarget, Thirst>,
            )
                .run_if(in_state(AnimalsPluginState::Done)),
        );
    }
}

#[derive(Default, Clone)]
pub(super) struct WaterSeekingBehaviorTreeParams {
    pub detect_water_params: Option<DetectWaterBehavior>,
    pub move_to_water_params: Option<MoveToWaterBehavior>,
    pub drink_water_params: Option<DrinkWaterBehavior>,
    pub on_fail_to_detect_water_tree: Option<Tree<Behave>>,
    pub move_to_water_tree: Option<Tree<Behave>>,
}

pub fn base_behavior(
    get_water_subtree: Tree<Behave>,
    get_food_subtree: Tree<Behave>,
    need_rest_subtree: Tree<Behave>,
    other_sub_tree: Tree<Behave>,
) -> Tree<Behave> {
    behave! {
        Behave::Forever => {
            Behave::Fallback => {
                Behave::While => {
                    Behave::trigger(NeuralNeedWater),
                    @ get_water_subtree
                },
                Behave::While => {
                    Behave::trigger(NeuralNeedFood),
                    @ get_food_subtree
                },
                Behave::While => {
                    Behave::trigger(NeuralNeedRest),
                    @ need_rest_subtree
                },
                @ other_sub_tree
            }
        }
    }
}

/// Creates a water-seeking behavior subtree
pub fn create_water_seeking_behavior(
    params: WaterSeekingBehaviorTreeParams,
) -> Tree<Behave> {
    let on_fail_to_detect_water =
        params.on_fail_to_detect_water_tree.unwrap_or_else(|| {
            behave! {
                Behave::Sequence => {
                    Behave::Wait(1.0),
                    Behave::AlwaysFail
                }
            }
        });

    let move_to_water_tree = params.move_to_water_tree.unwrap_or_else(|| behave! {
        Behave::spawn_named("(Common) Move To Water", params.move_to_water_params.unwrap_or_default())
    });

    behave! {
        Behave::Sequence => {
            Behave::IfThen => {
                // First detect water
                Behave::spawn_named("(Common) Detect Water", params.detect_water_params.unwrap_or_default()),
                // Then move to the water
                @ move_to_water_tree,
                // If we fail to detect water, run this tree
                @ on_fail_to_detect_water
            },
            // Finally drink the water
            Behave::spawn_named("(Common) Drink Water", params.drink_water_params.unwrap_or_default())
        }
    }
}
