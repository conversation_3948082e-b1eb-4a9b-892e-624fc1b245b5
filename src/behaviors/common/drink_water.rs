use crate::behaviors::common::water_detection::WaterSourceTarget;
use crate::components::environment::{Terrain, TerrainType};
use crate::components::lifecycle::Thirst;
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::*;
use bevy_behave::prelude::*;
use rand::Rng;

/// Behavior component for drinking water
#[derive(Component, Clone)]
pub struct DrinkWaterBehavior {
    /// How much water to drink per second
    pub drinking_rate: f32,
    /// How long to drink for
    pub generate_drinking_time: fn() -> f32,
    current_drinking_time: f32,
    generated_drinking_time: f32,
}

impl Default for DrinkWaterBehavior {
    fn default() -> Self {
        Self {
            drinking_rate: 10.0,
            generate_drinking_time: || rand::thread_rng().gen_range(1.0..3.0),
            current_drinking_time: 0.0,
            generated_drinking_time: 0.0,
        }
    }
}

impl DrinkWaterBehavior {
    pub fn new(drinking_rate: f32, generate_drinking_time: fn() -> f32) -> Self {
        Self {
            drinking_rate,
            generate_drinking_time,
            current_drinking_time: 0.0,
            generated_drinking_time: 0.0,
        }
    }
}

/// System to handle drinking water
pub fn drink_water_behavior_system(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &mut DrinkWaterBehavior)>,
    mut organism_query: Query<(
        &mut Thirst,
        &mut LinearVelocity,
        &mut AngularVelocity,
        &WaterSourceTarget,
    )>,
    water_query: Query<&Terrain>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (ctx, mut behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        // Get organism components
        if let Ok((mut thirst, mut linear_velocity, mut angular_velocity, water_target)) =
            organism_query.get_mut(target_entity)
        {
            // Get water source terrain
            if let Ok(terrain) = water_query.get(water_target.0) {
                // Verify it's still water
                if let TerrainType::Water(_) = terrain.terrain_type {
                    if behavior.generated_drinking_time == 0.0 {
                        behavior.generated_drinking_time =
                            (behavior.generate_drinking_time)();
                    }

                    // Stop moving
                    linear_velocity.0 = Vec3::ZERO;
                    angular_velocity.0 = Vec3::ZERO;

                    // // Bob up and down over time (sine wave)
                    // linear_velocity.0.y =
                    //     f32::sin(behavior.current_drinking_time * 2.0) * 0.05;
                    // linear_velocity.0.x = 0.0;
                    // linear_velocity.0.z = 0.0;

                    // Increment drinking time
                    behavior.current_drinking_time += delta_time.0;

                    // Calculate how much water to drink this frame
                    let water_amount = behavior.drinking_rate * delta_time.0;

                    // Drink water
                    thirst.drink(water_amount);

                    // If critical/dehydrated, don't stop drinking until we're above the critical threshold
                    if thirst.is_critical()
                        && thirst.current_thirst < thirst.critical_threshold
                    {
                        continue;
                    }

                    // Check if we've been drinking long enough or are fully hydrated
                    if behavior.current_drinking_time >= behavior.generated_drinking_time
                        || thirst.current_thirst >= thirst.max_thirst
                    {
                        // Reset drinking time
                        behavior.current_drinking_time = 0.0;

                        // Remove water target
                        commands.entity(target_entity).remove::<WaterSourceTarget>();

                        // Success
                        commands.trigger(ctx.success());
                        continue;
                    }
                } else {
                    // Not water anymore
                    commands.entity(target_entity).remove::<WaterSourceTarget>();
                    commands.trigger(ctx.failure());
                }
            } else {
                // Water source no longer exists
                commands.entity(target_entity).remove::<WaterSourceTarget>();
                commands.trigger(ctx.failure());
            }
        }
    }
}
