use crate::components::environment::{Terrain, TerrainType, WaterType};
use crate::components::lifecycle::{CurrentTerrainPod, Thirst};
use crate::components::perception::{AIPerception, MemoryCategory, StimulusSources, StimulusType};
use crate::resources::WorldSimulationDeltaTime;
use bevy::prelude::*;
use bevy_behave::prelude::*;

/// Behavior component for detecting water sources
#[derive(Reflect, Component, Clone)]
#[reflect(Component)]
pub struct DetectWaterBehavior {
    /// Minimum thirst level to start looking for water (0.0 - 1.0)
    pub thirst_threshold: f32,
    /// How long to search for water before giving up
    pub search_time: f32,
    /// Current search time
    pub current_search_time: f32,
}

impl Default for DetectWaterBehavior {
    fn default() -> Self {
        Self {
            thirst_threshold: 0.4, // Start looking for water when thirst is at 40%
            search_time: 5.0,
            current_search_time: 0.0,
        }
    }
}

/// Component to store the detected water source
#[derive(Reflect, Component, Debug, Clone, Copy)]
#[component(storage = "SparseSet")]
#[reflect(Component)]
pub struct WaterSourceTarget(pub Entity);

impl Into<Entity> for WaterSourceTarget {
    fn into(self) -> Entity {
        self.0
    }
}

/// Trigger event for checking if organism needs water
#[derive(Clone)]
pub struct NeedWater;

/// System to check if an organism needs water
pub fn needs_water(
    trigger: Trigger<BehaveTrigger<NeedWater>>,
    query: Query<&Thirst>,
    mut commands: Commands,
) {
    let ctx = trigger.event().ctx();

    if let Ok(thirst) = query.get(ctx.target_entity()) {
        if thirst.is_critical() || thirst.is_dehydrated() {
            log::info!("Organism needs water");
            commands.trigger(ctx.success());
            return;
        }
    }

    commands.trigger(ctx.failure());
}

/// System to detect water sources in the environment
pub fn detect_water_behavior_system(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &mut DetectWaterBehavior)>,
    organism_query: Query<(&AIPerception, &Thirst)>,
    terrain_query: Query<&Terrain>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (ctx, mut behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        // Get organism components
        if let Ok((perception, thirst)) = organism_query.get(target_entity) {
            // Increment search time
            behavior.current_search_time += delta_time.0;

            // Check if we've been searching too long
            if behavior.current_search_time > behavior.search_time {
                // Reset search time and fail
                behavior.current_search_time = 0.0;
                commands.trigger(ctx.failure());
                continue;
            }

            let mut found_water_source = None;

            // Check long-term memory for water sources
            if found_water_source.is_none() {
                for memory in
                    perception.long_term_memory_by_category_iter(MemoryCategory::Resource)
                {
                    if let Some(source_entity) = memory.stimulus.source_entity {
                        if let Ok(terrain) = terrain_query.get(source_entity) {
                            if let TerrainType::Water(_) = terrain.terrain_type {
                                log::info!(
                                    "Organism found water through long-term memory: {:?}",
                                    source_entity
                                );

                                found_water_source = Some(source_entity);
                                break;
                            }
                        }
                    }
                }
            }

            // Check if we can detect any water sources through perception (short term)
            if found_water_source.is_none() {
                for stimulus in perception.detected_smell_stimuli_iter() {
                    let source_entity = stimulus.source_entity.unwrap();
                    if let Ok(terrain) = terrain_query.get(source_entity) {
                        if let TerrainType::Water(_) = terrain.terrain_type {
                            // log::info!(
                            //     "Organism found water through perception: {:?}",
                            //     source_entity
                            // );

                            found_water_source = Some(source_entity);
                            break;
                        }
                    }
                }
            }

            if let Some(source_entity) = found_water_source {
                commands
                    .entity(target_entity)
                    .insert(WaterSourceTarget(source_entity));

                commands.trigger(ctx.success());
                return;
            }
        }

        // If we get here, we didn't find water
        commands.trigger(ctx.failure());
    }
}
