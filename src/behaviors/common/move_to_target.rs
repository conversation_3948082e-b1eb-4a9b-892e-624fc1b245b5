use crate::components::animals::{AnimalMoveToTargetParams, AnimalMovementBehavior};
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::ecs::observer::TriggerTargets;
use bevy::prelude::{
    Commands, Component, ContainsEntity, Entity, GlobalTransform, Name, ParamSet, Query, Res, Transform, With, Without
};
use bevy::reflect::GetTypeRegistration;
use bevy_behave::prelude::BehaveCtx;

pub trait MoveToTargetBehavior {
    fn get_max_turn_speed(&self) -> f32;
    fn get_turn_acceleration(&self) -> f32;
    fn get_target_distance(&self) -> Option<f32> {
        None
    }
}

pub fn move_to_target<Animal, Behavior, Target, Required>(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &Behavior)>,
    mut organism_query: Query<
        (
            &mut Animal,
            &mut GlobalTransform,
            &mut LinearVelocity,
            &mut AngularVelocity,
            &Target,
        ),
        With<Required>,
    >,
    target_query: Query<&GlobalTransform, Without<Required>>,
    delta_time: Res<WorldSimulationDeltaTime>,
) where
    Animal:
        AnimalMovementBehavior + Component<Mutability = bevy::ecs::component::Mutable>,
    Behavior: Component + MoveToTargetBehavior,
    Required: Component,
    Target: Component + Into<Entity> + Copy,
{
    for (ctx, behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        if let Ok((
            mut animal,
            mut transform,
            mut linear_velocity,
            mut angular_velocity,
            target,
        )) = organism_query.get_mut(target_entity)
        {
            // Get target transform
            if let Ok(target_transform) = target_query.get((*target).into()) {
                let target_pos = target_transform.translation();
                let current_speed = animal.get_speed();

                animal.move_towards_target(
                    &mut transform,
                    &mut linear_velocity,
                    &mut angular_velocity,
                    AnimalMoveToTargetParams {
                        current_speed,
                        target: target_pos,
                        delta: delta_time.0,
                        max_turn_speed: behavior.get_max_turn_speed(),
                        turn_acceleration: behavior.get_turn_acceleration(),
                    },
                );

                let distance = transform.translation().distance(target_pos);

                if distance
                    <= behavior
                        .get_target_distance()
                        .unwrap_or(animal.get_target_distance())
                {
                    commands.trigger(ctx.success());
                    continue;
                }
            } else {
                log::info!("Target no longer exists");
                // Target no longer exists
                commands.entity(target_entity).remove::<Target>();
                commands.trigger(ctx.failure());
            }
        }
    }
}
