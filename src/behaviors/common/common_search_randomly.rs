use crate::components::animals::{AnimalMoveToTargetParams, AnimalMovementBehavior};
use crate::components::lifecycle::Stamina;
use crate::resources::environment::TerrainPodGrid;
use crate::resources::WorldSimulationDeltaTime;
use crate::systems::environment::system_params::TerrainSystemParams;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::math::Vec3;
use bevy::prelude::*;
use bevy_behave::prelude::BehaveCtx;
use rand::Rng;

#[derive(Reflect, Default, Component, Clone)]
#[reflect(Component)]
pub struct CommonSearchRandomlyBehavior {
    target: Option<Vec3>,
    time: f32,
    pub max_time: f32,
    /// Maximum turning speed in radians per second
    pub max_turn_speed: f32,
    /// How quickly the organism can change its turning speed (angular acceleration)
    pub turn_acceleration: f32,
}

impl CommonSearchRandomlyBehavior {
    pub fn new(max_time: f32) -> Self {
        Self {
            max_time,
            time: 0.0,
            target: None,
            max_turn_speed: 2.0, // 2 radians per second (~115 degrees/sec)
            turn_acceleration: 4.0, // How quickly we can change turn speed
        }
    }

    pub fn new_with_turn_params(
        max_time: f32,
        max_turn_speed: f32,
        turn_acceleration: f32,
    ) -> Self {
        Self {
            max_time,
            time: 0.0,
            target: None,
            max_turn_speed,
            turn_acceleration,
        }
    }
}

pub fn common_search_randomly_behavior<Animal>(
    mut commands: Commands,
    mut tasks: Query<(&BehaveCtx, &mut CommonSearchRandomlyBehavior)>,
    mut organism_query: Query<(
        &mut GlobalTransform,
        &mut LinearVelocity,
        &mut AngularVelocity,
        &mut Animal,
    )>,
    mut stamina_query: Query<&mut Stamina>,
    delta_time: Res<WorldSimulationDeltaTime>,
    terrain_pod_grid: Res<TerrainPodGrid>,
) where
    Animal:
        AnimalMovementBehavior + Component<Mutability = bevy::ecs::component::Mutable>,
{
    for (ctx, mut behavior) in tasks.iter_mut() {
        let target_entity = ctx.target_entity();

        if let Ok((
            mut transform,
            mut linear_velocity,
            mut angular_velocity,
            mut animal,
        )) = organism_query.get_mut(target_entity)
        {
            behavior.time += delta_time.0;

            if behavior.target.is_none() {
                let mut target = terrain_pod_grid.get_random_position_in_pod();
                target.y = 0.25;

                // Ensure the target is above the terrain (grass)
                behavior.target = Some(target);
            }

            let target = behavior.target.unwrap_or_default();
            let mut speed = animal.get_speed();

            // Have stamina affect speed after a threshold
            if let Ok(mut stamina) = stamina_query.get_mut(target_entity) {
                stamina.deplete(delta_time.0);

                if stamina.normalized_stamina() < 0.5 {
                    speed *= stamina.normalized_stamina();
                }
            }

            animal.move_towards_target(
                &mut transform,
                &mut linear_velocity,
                &mut angular_velocity,
                AnimalMoveToTargetParams {
                    target,
                    current_speed: speed,
                    max_turn_speed: behavior.max_turn_speed,
                    turn_acceleration: behavior.turn_acceleration,
                    delta: delta_time.0,
                },
            );

            let distance = transform.translation().distance(target);

            if distance <= 0.1 {
                behavior.target = None;
            }

            if behavior.time > behavior.max_time * rand::thread_rng().gen_range(0.85..1.)
            {
                behavior.target = None;
                commands.trigger(ctx.success());
                continue;
            }
        }
    }
}
