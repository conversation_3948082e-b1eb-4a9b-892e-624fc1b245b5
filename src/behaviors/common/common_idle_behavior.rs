use crate::components::animals::bees::Bee;
use crate::components::lifecycle::{BiologicalOrganism, Stamina};
use crate::resources::environment::TerrainPodGrid;
use crate::resources::WorldSimulationDeltaTime;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::math::Vec3;
use bevy::prelude::{
    Commands, Component, Query, Reflect, ReflectComponent, Res, Transform, With
};
use bevy_behave::prelude::BehaveCtx;
use rand::Rng;

#[derive(Reflect, Component, Clone, Default)]
#[reflect(Component)]
pub struct CommonIdleBehavior {
    pub idle_time: f32,
    pub max_idle_time: f32,
}

impl CommonIdleBehavior {
    pub fn new(max_idle_time: f32) -> Self {
        Self {
            max_idle_time,
            idle_time: 0.0,
        }
    }
}

pub fn common_idle_behavior(
    mut commands: Commands,
    mut idle_tasks: Query<(&mut CommonIdleBehavior, &BehaveCtx)>,
    mut organism_query: Query<
        (&mut Transform, &mut LinearVelocity, &mut AngularVelocity),
        With<BiologicalOrganism>,
    >,
    mut stamina_query: Query<&mut Stamina>,
    terrain_pod_grid: Res<TerrainPodGrid>,
    delta_time: Res<WorldSimulationDeltaTime>,
) {
    for (mut idle, ctx) in idle_tasks.iter_mut() {
        // Increment idle time
        idle.idle_time += delta_time.0;

        if let Ok((mut transform, mut linear_velocity, mut angular_velocity)) =
            organism_query.get_mut(ctx.target_entity())
        {
            if transform.translation.y < 0. {
                linear_velocity.0 = Vec3::ZERO;
                transform.translation.y = terrain_pod_grid.get_terrain_min_height();
            }

            // Slow down linear velocity
            linear_velocity.0 *= 0.95;
            angular_velocity.0 = Vec3::ZERO;

            if let Ok(mut stamina) = stamina_query.get_mut(ctx.target_entity()) {
                stamina.regenerate(delta_time.0);
            }
        }

        // If we've idled long enough, report success
        if idle.idle_time >= idle.max_idle_time * rand::thread_rng().gen_range(0.5..1.) {
            commands.trigger(ctx.success());
        }
    }
}
