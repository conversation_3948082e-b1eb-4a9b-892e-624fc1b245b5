mod animations;

use crate::behaviors::common::base_behavior;
use crate::behaviors::common::common_idle_behavior::CommonIdleBehavior;
use crate::behaviors::common::common_search_randomly::{
    common_search_randomly_behavior, CommonSearchRandomlyBehavior
};
use crate::behaviors::neural::needs_behaviors::NeuralNeedRest;
use crate::components::ai::neural_network::NeuralBrain;
use crate::components::animals::deer::Deer;
use crate::components::animals::{
    AnimalMovementBehavior, AutoRigColliders, MovementDampingFactor
};
use crate::components::perception::{AIPerception, SenseType, SmellPerception};
use crate::plugins::animals::AnimalsPluginState;
use crate::resources::animals::AnimalsAssetsKeys;
use crate::systems::ai::neural_training::NeuralTrainingData;
use crate::systems::animals::system_params::AnimalsAssetSystemParams;
use avian3d::prelude::{
    GravityScale, LinearVelocity, Mass, <PERSON><PERSON><PERSON><PERSON><PERSON>peed, <PERSON><PERSON><PERSON>ar<PERSON>peed, <PERSON>igid<PERSON><PERSON>
};
use bevy::prelude::*;
use bevy_behave::prelude::{BehaveTargetEntity, BehaveTimeout, BehaveTree, Tree};
use bevy_behave::{behave, Behave};
use rand::Rng;

pub struct DeerBehaviorsPlugin;

impl Plugin for DeerBehaviorsPlugin {
    fn build(&self, app: &mut App) {
        app.register_type::<Deer>()
            // Note: Animation system is now handled by the generic animation system
            .add_systems(
                FixedUpdate,
                (common_search_randomly_behavior::<Deer>,)
                    .run_if(in_state(AnimalsPluginState::Done)),
            );
    }
}

pub fn spawn_deer_factory(
    commands: &mut Commands,
    animals_assets: &mut AnimalsAssetSystemParams,
) {
    let scale = 0.25;
    if let Some(scene_root) = animals_assets.get_model(AnimalsAssetsKeys::DEER) {
        let deer = Deer {
            acceleration_speed: 20.0,
            ..default()
        };

        let deer_entity = commands
            .spawn((
                scene_root,
                deer,
                AutoRigColliders,
                Transform::from_translation(Vec3::new(0.5, 0.25, 0.5))
                    .with_scale(Vec3::splat(scale)),
            ))
            .insert((NeuralTrainingData::default(), NeuralBrain::new_base()))
            .insert((
                // Perception components
                SmellPerception {
                    smell_range: 30.0,
                    ..default()
                },
                AIPerception {
                    active_senses: SenseType::get_all(),
                    ..default()
                },
            ))
            .insert((
                // Physics components
                RigidBody::Kinematic,
                LinearVelocity::ZERO,
                GravityScale(1.0),
                Mass(5.0),
                MaxLinearSpeed(deer.get_speed()),
                MaxAngularSpeed(deer.get_speed() / 2.),
            ))
            .insert(MovementDampingFactor(0.1))
            .id();

        commands
            .spawn((
                Name::new("Behavior Tree"),
                BehaveTree::new(create_neural_needs_behavior_tree(&deer))
                    .with_logging(false),
                BehaveTargetEntity::Entity(deer_entity),
            ))
            .insert(ChildOf(deer_entity));
    };
}

fn create_neural_needs_behavior_tree(deer: &Deer) -> Tree<Behave> {
    base_behavior(
        behave! {
            Behave::AlwaysSucceed
        },
        behave! {
            Behave::AlwaysSucceed
        },
        behave! {
            Behave::Sequence => {
                Behave::spawn_named("Idle", (CommonIdleBehavior::new(10.0), BehaveTimeout::from_secs(100.0, false))),
                Behave::Wait(3.0 * rand::thread_rng().gen_range(0.5..1.5))
            }
        },
        behave! {
            Behave::Fallback => {
                Behave::Sequence => {
                    Behave::spawn_named("Move Randomly", (
                        CommonSearchRandomlyBehavior::new(10.0 * rand::thread_rng().gen_range(0.5..1.5)),
                        BehaveTimeout::from_secs(15.1, false)
                    )),
                    Behave::Wait(1.0 * rand::thread_rng().gen_range(0.5..1.5))
                },
            }
        },
    )
}
