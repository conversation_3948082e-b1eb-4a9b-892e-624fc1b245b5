mod animations;

use crate::behaviors::common::common_idle_behavior::CommonIdleBehavior;
use crate::behaviors::common::common_search_randomly::CommonSearchRandomlyBehavior;
use crate::behaviors::common::{
    base_behavior, create_food_seeking_behavior, create_water_seeking_behavior, CommonBehaviorsPlugin, DetectFoodBehavior, DrinkWaterBehavior, EatFoodBehavior, FoodSeekingBehaviorTreeParams, MoveToFoodBehavior, MoveToWaterBehavior, WaterSeekingBehaviorTreeParams
};
use crate::components::ai::neural_network::NeuralBrain;
use crate::components::animals::deer::Deer;
use crate::components::animals::{AnimalMovementBehavior, AnimalSpeciesModelKey, AnimalSpeciesType, AutoSetupIK, MovementDampingFactor};
use crate::components::ecosystem::FoodType;
use crate::components::perception::{AIPerception, SenseType, SmellPerception};
use crate::resources::animals::AnimalsAssetsKeys;
use crate::systems::ai::neural_training::NeuralTrainingData;
use crate::systems::animals::system_params::AnimalsAssetSystemParams;
use avian3d::prelude::{
    GravityScale, LinearVelocity, Mass, MaxAngularSpeed, MaxLinearSpeed, RigidBody
};
use bevy::prelude::*;
use bevy_behave::prelude::{BehaveTargetEntity, BehaveTimeout, BehaveTree, Tree};
use bevy_behave::{behave, Behave};
use rand::Rng;

pub struct DeerBehaviorsPlugin;

impl Plugin for DeerBehaviorsPlugin {
    fn build(&self, app: &mut App) {
        app.register_type::<Deer>()
            .add_plugins(CommonBehaviorsPlugin::<Deer>::default());
    }
}

pub fn spawn_deer_factory(
    commands: &mut Commands,
    animals_assets: &mut AnimalsAssetSystemParams,
    spawn_position: Vec3,
) -> Option<Entity> {
    // let scale = 0.25;
    let scale = 1.0;

    let deer = Deer {
        acceleration_speed: 20.0,
        ..default()
    };

    let deer_entity = commands
        .spawn((
            AnimalSpeciesType::Deer,
            AnimalSpeciesModelKey::new(AnimalsAssetsKeys::DEER_MALE),
            deer,
            AutoSetupIK,
            // AutoRigColliders,
            Transform::from_translation(spawn_position).with_scale(Vec3::splat(scale)),
        ))
        .insert((NeuralTrainingData::default(), NeuralBrain::new_base()))
        .insert((
            // Perception components
            SmellPerception {
                smell_range: 30.0,
                ..default()
            },
            AIPerception {
                active_senses: SenseType::get_all(),
                ..default()
            },
        ))
        .insert((
            // Physics components
            RigidBody::Kinematic,
            LinearVelocity::ZERO,
            GravityScale(1.0),
            Mass(5.0),
            MaxLinearSpeed(deer.get_speed()),
            MaxAngularSpeed(deer.get_speed() / 2.),
        ))
        .insert(MovementDampingFactor(0.1))
        .id();

    // commands
    //     .spawn((
    //         Name::new("Behavior Tree"),
    //         BehaveTree::new(create_neural_needs_behavior_tree(&deer)).with_logging(false),
    //         BehaveTargetEntity::Entity(deer_entity),
    //     ))
    //     .insert(ChildOf(deer_entity));

    Some(deer_entity)
}

fn create_neural_needs_behavior_tree(deer: &Deer) -> Tree<Behave> {
    let water_subtree = create_water_seeking_behavior(WaterSeekingBehaviorTreeParams {
        drink_water_params: Some(DrinkWaterBehavior::new(
            rand::thread_rng().gen_range(3.0..6.0),
            || rand::thread_rng().gen_range(3.0..10.0),
        )),
        move_to_water_tree: Some(behave! {
            Behave::spawn_named("Move To Water", (MoveToWaterBehavior {
                max_speed: deer.acceleration_speed,
                not_used_for_common_behavior: true,
                ..default()
            }, BehaveTimeout::from_secs(100.0, false))),
        }),
        ..default()
    });

    let food_subtree = create_food_seeking_behavior(FoodSeekingBehaviorTreeParams {
        eat_food_params: Some(EatFoodBehavior {
            eating_rate: 1.0,
            ..default()
        }),
        detect_food_params: Some(DetectFoodBehavior {
            search_time: 6.0,
            consumable_food_types: vec![
                FoodType::Plant,
                FoodType::Nectar,
                FoodType::Pollen,
            ],
            ..default()
        }),
        move_to_food_tree: Some(behave! {
            Behave::spawn_named("Move To Food", (MoveToFoodBehavior {
                max_speed: deer.acceleration_speed,
                ..default()
            }, BehaveTimeout::from_secs(100.0, false))),
        }),
        ..default()
    });

    base_behavior(
        water_subtree,
        food_subtree,
        behave! {
            Behave::Sequence => {
                Behave::spawn_named("Idle", (CommonIdleBehavior::new(10.0), BehaveTimeout::from_secs(100.0, false))),
                Behave::Wait(3.0 * rand::thread_rng().gen_range(0.5..1.5))
            }
        },
        behave! {
            Behave::Fallback => {
                Behave::Sequence => {
                    Behave::spawn_named("Move Randomly", (
                        CommonSearchRandomlyBehavior::new(10.0 * rand::thread_rng().gen_range(0.5..1.5)),
                        BehaveTimeout::from_secs(15.1, false)
                    )),
                    Behave::Wait(1.0 * rand::thread_rng().gen_range(0.5..1.5))
                },
            }
        },
    )
}
