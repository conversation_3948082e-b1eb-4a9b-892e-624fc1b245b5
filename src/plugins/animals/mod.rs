use crate::behaviors::{BaseBehaviorsPlugin, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lug<PERSON>, DeerBehaviorsPlugin};
use crate::components::animals::animation_state::{
    AnimationBlendState, AnimationConfig, AnimationState, DirectionTracker
};
use crate::components::animals::deer::{Deer, DeerQuadrupedBaseArmature};
use crate::components::animals::{
    AnimalSpeciesModelKey, AnimalSpeciesType, MovementDampingFactor
};
use crate::resources::animals::AnimalsAssets;
use crate::systems::animals::{apply_movement_damping, on_added_gltf_scene, on_auto_setup_ik, set_model_for_species};
use bevy::prelude::*;
use bevy_asset_loader::loading_state::{LoadingState, LoadingStateAppExt};
use bevy_asset_loader::prelude::ConfigureLoadingState;
use bevy_descendant_collector::{DescendantCollectorPlugin, HierarchyRootPosition};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Debug, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>)]
pub enum AnimalsPluginState {
    #[default]
    LoadingAssets,
    Done,
}

pub struct AnimalsPlugin;

impl Plugin for AnimalsPlugin {
    fn build(&self, app: &mut App) {
        app.init_state::<AnimalsPluginState>()
            .add_loading_state(
                LoadingState::new(AnimalsPluginState::LoadingAssets)
                    .continue_to_state(AnimalsPluginState::Done)
                    .load_collection::<AnimalsAssets>(),
            )
            .add_plugins(DescendantCollectorPlugin::<DeerQuadrupedBaseArmature>::new(
                HierarchyRootPosition::Scene,
            ))
            .register_type::<MovementDampingFactor>()
            .register_type::<AnimalSpeciesModelKey>()
            .register_type::<AnimalSpeciesType>()
            .register_type::<DeerQuadrupedBaseArmature>()
            .register_type::<AnimationState>()
            .register_type::<AnimationConfig>()
            .register_type::<DirectionTracker>()
            .register_type::<AnimationBlendState>()
            .add_systems(Update, on_added_gltf_scene::<Deer, DeerQuadrupedBaseArmature>)
            .add_systems(Update, on_auto_setup_ik::<DeerQuadrupedBaseArmature>)
            // .add_systems(Update, auto_rig_colliders::<AnimalQuadrupedBaseArmature>)
            .add_systems(Update, apply_movement_damping)
            .add_systems(
                Update,
                set_model_for_species
                    .run_if(resource_exists::<AnimalsAssets>)
                    .run_if(in_state(AnimalsPluginState::Done)),
            )
            // .add_systems(Update, initialize_animation_components)
            // .add_systems(Update, initialize_blending_components)
            // .add_systems(Update, setup_animation_graphs)
            // .add_systems(Update, update_direction_tracking)
            // .add_systems(
            //     Update,
            //     blended_animation_system.after(update_direction_tracking),
            // )
            .add_plugins(AnimalBehaviorsPlugin);
    }
}

pub struct AnimalBehaviorsPlugin;

impl Plugin for AnimalBehaviorsPlugin {
    fn build(&self, app: &mut App) {
        app
            // Add common behaviors that can be used by any animal
            .add_plugins(BaseBehaviorsPlugin)
            .add_plugins(DeerBehaviorsPlugin)
            .add_plugins(BeeBehaviorsPlugin);
    }
}
