use crate::behaviors::neural::needs_behaviors::{
    neural_needs_food, neural_needs_rest, neural_needs_water
};
use crate::components::ai::neural_network::NeuralBrain;
use crate::systems::ai::*;
use bevy::prelude::*;
use bevy::time::common_conditions::on_timer;
use std::time::Duration;

pub struct NeuralNetworkPlugin;

impl Plugin for NeuralNetworkPlugin {
    fn build(&self, app: &mut App) {
        app.register_type::<NeuralBrain>()
            .add_plugins(animals_neural_networks::BeeNeuralNetworkPlugin)
            .add_systems(
                FixedUpdate,
                neural_training::train_neural_networks
                    .run_if(on_timer(Duration::from_millis(1000))),
            )
            .add_systems(
                FixedUpdate,
                (
                    neural_training::collect_training_data,
                    neural_network_system::update_neural_inputs,
                ),
            );
    }
}

pub struct NeuralBehaviorsPlugin;

impl Plugin for NeuralBehaviorsPlugin {
    fn build(&self, app: &mut App) {
        app.add_observer(neural_needs_rest)
            .add_observer(neural_needs_food)
            .add_observer(neural_needs_water);
    }
}

mod animals_neural_networks {
    use super::*;

    pub struct BeeNeuralNetworkPlugin;

    impl Plugin for BeeNeuralNetworkPlugin {
        fn build(&self, app: &mut App) {
            app.add_systems(
                FixedUpdate,
                (
                    animals::bee_neural_training::train_bee_neural_networks
                        .run_if(on_timer(Duration::from_millis(50))),
                    animals::bee_neural_training::collect_bee_training_data,
                    animals::bee_neural_training::update_bee_neural_inputs,
                ),
            );
        }
    }
}
