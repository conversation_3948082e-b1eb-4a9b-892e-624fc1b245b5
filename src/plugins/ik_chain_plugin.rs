use bevy::prelude::*;
use crate::libraries::ik_fabrik::IkSystem;
use crate::systems::ik_chain::{
    update_quadruped_ik_system, setup_quadruped_ik_system, update_gait_system,
    QuadrupedIk<PERSON><PERSON>roller, Foot<PERSON>k<PERSON><PERSON><PERSON>, Gait<PERSON><PERSON>roller, FootState, LegType
};

/// Plugin for procedural IK-based walking for quadrupeds
pub struct IkChainPlugin;

impl Plugin for IkChainPlugin {
    fn build(&self, app: &mut App) {
        app
            // Register components
            .register_type::<QuadrupedIkController>()
            .register_type::<FootIkChain>()
            .register_type::<GaitController>()
            .register_type::<FootState>()
            .register_type::<LegType>()
            
            // Add systems
            .add_systems(
                Update,
                setup_quadruped_ik_system
            )
            .add_systems(
                PostUpdate,
                (
                    update_gait_system,
                    update_quadruped_ik_system,
                )
                .chain()
                .after(TransformSystem::TransformPropagate)
                .before(IkSystem::Solve)
            );
    }
}
