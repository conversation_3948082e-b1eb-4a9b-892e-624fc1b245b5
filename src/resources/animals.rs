use bevy::asset::Handle;
use bevy::gltf::Gltf;
use bevy::platform::collections::HashMap;
use bevy::prelude::{Component, Reflect, Res, Resource};
use bevy_asset_loader::asset_collection::AssetCollection;
use bevy_gltf_animation::prelude::GltfSceneRoot;

#[derive(AssetCollection, Resource, Debug)]
pub struct AnimalsAssets {
    #[asset(path = "models/animals", collection(mapped, typed))]
    pub models: HashMap<String, Handle<Gltf>>,
}

#[allow(non_snake_case, non_upper_case_globals)]
pub mod AnimalsAssetsKeys {
    pub const QUIRKY_SERIES: &'static str = "quirky_series";
    pub const BEE: &'static str = "Bee";
    pub const BEE_HIVE: &'static str = "Bee_Hive";
    pub const PUDU: &'static str = "Pudu_Animations";
    pub const DEER_MALE: &'static str = "deer_001";
    pub const DEER_FEMALE: &'static str = "deer_female_001";
    pub const DEER_CUB_1: &'static str = "deer_cub_001";
    pub const DEER_CUB_2: &'static str = "deer_cub_002";
}

impl AnimalsAssets {
    pub fn get_model(&self, name: &str, folder: Option<&str>) -> Option<GltfSceneRoot> {
        let mut input_name = name.replace(".gltf", "").replace(".glb", "");
        if let Some(folder) = folder {
            input_name = format!("{}/{}", folder, input_name);
        }

        let path = format!("models/animals/{}.glb", input_name);

        let model_handle = self
            .models
            .get(path.as_str())
            .or_else(|| self.models.get(path.replace(".glb", ".gltf").as_str()));

        model_handle.and_then(|handle| Some(GltfSceneRoot::new(handle.clone())))
    }
}
