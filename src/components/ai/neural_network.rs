use bevy::platform::collections::HashMap;
use bevy::prelude::*;

use neuroflow::data::{DataSet, Extractable};
use neuroflow::FeedForward;

pub struct NeuralNetwork {
    pub nn: FeedForward,
}

impl Default for NeuralNetwork {
    fn default() -> Self {
        Self {
            nn: FeedForward::new(&[1, 1, 1]),
        }
    }
}

pub const NEURAL_NETWORK_PATH: &str = "assets/data/animals/neural_network";

impl NeuralNetwork {
    pub fn new(layers: &[i32]) -> Self {
        let network = FeedForward::new(layers);

        Self { nn: network }
    }

    pub fn load_nn(&mut self, path: &str) -> bool {
        if let Ok(nn) = neuroflow::io::load(path) {
            log::info!("Loaded neural network from {}", path);
            self.nn = nn;
            true
        } else {
            log::error!("Failed to load neural network from {}", path);
            false
        }
    }

    pub fn save_nn(&mut self, path: &str) {
        if let Err(e) = neuroflow::io::save(&mut self.nn, path) {
            log::error!("Error saving neural network: {:?}", e);
        }
    }

    pub fn calculate(&mut self, inputs: &[f64]) -> &[f64] {
        self.nn.calc(inputs)
    }

    pub fn train(&mut self, data_set: &DataSet, iterations: i64) {
        self.nn.learning_rate(0.1).train(data_set, iterations);
    }
}

/// Species types that have unique neural network configurations
#[derive(Reflect, Clone, Debug, PartialEq, Eq, Hash)]
pub enum SpeciesType {
    Generic,
    Bee,
    Bird,
    Mammal,
    Fish,
    Reptile,
    // Add more as needed
}

/// Base input types that all animals share
#[derive(Reflect, Clone, Debug, PartialEq, Eq, Hash)]
pub enum BaseInputType {
    Thirst,
    Hunger,
    Stamina,
    TimeOfDay,
    StateNovelty,
    DistanceToUnexploredArea,
    DistanceToPredator,
    ThreatBearing,
    CoverProximity,
    GroupDensity,
    ResourceGradient,
    AmbientVisibility,
}

/// Base output types that all animals share
#[derive(Reflect, Clone, Debug, PartialEq, Eq, Hash)]
pub enum BaseOutputType {
    Drink,
    Eat,
    Rest,
    Explore,
    Flee,
    Attack,
    Hide,
    Patrol,
}

#[derive(Reflect, Clone, Debug)]
pub struct InputConfig {
    // Maps base input types to their index in the inputs vector
    pub base_mapping: HashMap<BaseInputType, usize>,

    // Maps species-specific input types (as strings) to their index
    pub extended_mapping: HashMap<String, usize>,

    // Descriptions for all inputs
    pub descriptions: HashMap<String, String>,

    // Total number of inputs (base + extended)
    pub total_inputs: usize,
}

#[derive(Reflect, Clone, Debug)]
pub struct OutputConfig {
    // Maps base output types to their index in the outputs vector
    pub base_mapping: HashMap<BaseOutputType, usize>,

    // Maps species-specific output types (as strings) to their index
    pub extended_mapping: HashMap<String, usize>,

    // Descriptions for all outputs
    pub descriptions: HashMap<String, String>,

    // Total number of outputs (base + extended)
    pub total_outputs: usize,
}

// Add to existing NeuralBrain struct
#[derive(Component, Reflect)]
#[reflect(Component)]
pub struct NeuralBrain {
    #[reflect(ignore)]
    pub network: NeuralNetwork,
    pub inputs: Vec<f64>,
    pub outputs: Vec<f64>,
    #[reflect(ignore)]
    pub last_update: f32,
    pub update_frequency: f32,

    // Species configuration
    pub species_type: SpeciesType,
    pub input_config: InputConfig,
    pub output_config: OutputConfig,
}

impl NeuralBrain {
    // Create a base neural brain with common inputs/outputs
    pub fn new_base() -> Self {
        // Create base input mapping
        let mut base_input_mapping = HashMap::new();
        let mut input_descriptions = HashMap::new();

        // Define base inputs that all animals share
        base_input_mapping.insert(BaseInputType::Thirst, 0);
        base_input_mapping.insert(BaseInputType::Hunger, 1);
        base_input_mapping.insert(BaseInputType::Stamina, 2);
        base_input_mapping.insert(BaseInputType::TimeOfDay, 3);
        base_input_mapping.insert(BaseInputType::StateNovelty, 4);
        base_input_mapping.insert(BaseInputType::DistanceToUnexploredArea, 5);
        base_input_mapping.insert(BaseInputType::DistanceToPredator, 6);
        base_input_mapping.insert(BaseInputType::ThreatBearing, 7);
        base_input_mapping.insert(BaseInputType::CoverProximity, 8);
        base_input_mapping.insert(BaseInputType::GroupDensity, 9);
        base_input_mapping.insert(BaseInputType::ResourceGradient, 10);
        base_input_mapping.insert(BaseInputType::AmbientVisibility, 11);

        // Add descriptions for base inputs
        input_descriptions.insert(
            "Thirst".to_string(),
            "Thirst level (0.0=dehydrated, 1.0=hydrated)".to_string(),
        );
        input_descriptions.insert(
            "Hunger".to_string(),
            "Hunger level (0.0=starving, 1.0=full)".to_string(),
        );
        input_descriptions.insert(
            "Stamina".to_string(),
            "Stamina level (0.0=exhausted, 1.0=rested)".to_string(),
        );
        input_descriptions.insert(
            "TimeOfDay".to_string(),
            "Time of day (0.0=early, 1.0=late)".to_string(),
        );
        input_descriptions.insert(
            "StateNovelty".to_string(),
            "State novelty (0.0=boring, 1.0=novel)".to_string(),
        );
        input_descriptions.insert(
            "DistanceToUnexploredArea".to_string(),
            "Distance to unexplored area (0.0=close, 1.0=far)".to_string(),
        );
        input_descriptions.insert(
            "DistanceToPredator".to_string(),
            "Distance to nearest predator (0.0=close, 1.0=far)".to_string(),
        );
        input_descriptions.insert(
            "ThreatBearing".to_string(),
            "Direction of the threat relative to the organism (0.0–1.0)".to_string(),
        );
        input_descriptions.insert(
            "CoverProximity".to_string(),
            "Proximity to cover (0.0=close, 1.0=far)".to_string(),
        );
        input_descriptions.insert(
            "GroupDensity".to_string(),
            "Local group density (0.0=sparse, 1.0=dense)".to_string(),
        );
        input_descriptions.insert(
            "ResourceGradient".to_string(),
            "Resource gradient (0.0–1.0, higher values mean resources are more abundant in the direction of movement)"
                .to_string(),
        );
        input_descriptions.insert(
            "AmbientVisibility".to_string(),
            "Ambient visibility (0.0–1.0, higher values mean better visibility)"
                .to_string(),
        );

        // Create base output mapping
        let mut base_output_mapping = HashMap::new();
        let mut output_descriptions = HashMap::new();

        // Define base outputs that all animals share
        base_output_mapping.insert(BaseOutputType::Drink, 0);
        base_output_mapping.insert(BaseOutputType::Eat, 1);
        base_output_mapping.insert(BaseOutputType::Rest, 2);
        base_output_mapping.insert(BaseOutputType::Explore, 3);
        base_output_mapping.insert(BaseOutputType::Flee, 4);
        base_output_mapping.insert(BaseOutputType::Attack, 5);
        base_output_mapping.insert(BaseOutputType::Hide, 6);
        base_output_mapping.insert(BaseOutputType::Patrol, 7);

        // Add descriptions for base outputs
        output_descriptions.insert("Drink".to_string(), "Drink water".to_string());
        output_descriptions.insert("Eat".to_string(), "Eat food".to_string());
        output_descriptions
            .insert("Rest".to_string(), "Rest to recover stamina".to_string());
        output_descriptions
            .insert("Explore".to_string(), "Explore new areas".to_string());
        output_descriptions.insert("Flee".to_string(), "Flee from predators".to_string());
        output_descriptions
            .insert("Attack".to_string(), "Attack other organisms".to_string());
        output_descriptions.insert("Hide".to_string(), "Hide from predators".to_string());
        output_descriptions
            .insert("Patrol".to_string(), "Patrol known areas".to_string());

        let total_inputs = base_input_mapping.len();
        let input_config = InputConfig {
            base_mapping: base_input_mapping,
            extended_mapping: HashMap::new(),
            descriptions: input_descriptions,
            total_inputs: total_inputs, // Number of base inputs
        };

        let total_outputs = base_output_mapping.len();
        let output_config = OutputConfig {
            base_mapping: base_output_mapping,
            extended_mapping: HashMap::new(),
            descriptions: output_descriptions,
            total_outputs: total_outputs, // Number of base outputs
        };

        // Create neural network with base layers
        let layers = vec![
            input_config.total_inputs as i32,
            10,
            8,
            8,
            10,
            output_config.total_outputs as i32,
        ];
        let mut network = NeuralNetwork::new(&layers);

        // Load or initialize base training data
        let path = format!("{NEURAL_NETWORK_PATH}/base.flow");
        if !network.load_nn(&path) {
            // Initialize with base training data
            Self::initialize_base_training_data(&mut network);
            network.save_nn(&path);
        }

        Self {
            network,
            inputs: vec![0.0; input_config.total_inputs],
            outputs: vec![0.0; output_config.total_outputs],
            last_update: 0.0,
            update_frequency: 0.2,
            species_type: SpeciesType::Generic,
            input_config,
            output_config,
        }
    }

    // Initialize base training data that all animals share
    fn initialize_base_training_data(network: &mut NeuralNetwork) {
        let mut data_set = DataSet::new();

        // 1. Basic survival - Thirst scenarios
        // Fully dehydrated → strong drink
        data_set.push(
            &[0.0, 1.0, 1.0, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Moderately dehydrated → moderate drink
        data_set.push(
            &[0.3, 0.8, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Slightly thirsty → slight drink
        data_set.push(
            &[0.6, 0.8, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.3, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Well-hydrated → no drink
        data_set.push(
            &[0.9, 0.8, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );

        // 2. Basic survival - Hunger scenarios
        // Starving → strong eat
        data_set.push(
            &[0.8, 0.0, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Moderately hungry → moderate eat
        data_set.push(
            &[0.8, 0.3, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Slightly hungry → slight eat
        data_set.push(
            &[0.8, 0.6, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.3, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Well-fed → no eat
        data_set.push(
            &[0.8, 0.9, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );

        // 3. Basic survival - Stamina scenarios
        // Exhausted → strong rest
        data_set.push(
            &[0.8, 0.8, 0.0, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Low stamina → moderate rest
        data_set.push(
            &[0.8, 0.8, 0.3, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.0, 0.7, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Slightly tired → slight rest
        data_set.push(
            &[0.8, 0.8, 0.6, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.0, 0.3, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Well-rested → no rest
        data_set.push(
            &[0.8, 0.8, 0.9, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );

        // 4. Exploration scenarios
        // Well-hydrated & well-fed & well-rested → strong explore
        data_set.push(
            &[0.9, 0.9, 0.9, 0.5, 0.8, 0.2, 0.8, 0.3, 0.5, 0.3, 0.7, 0.9],
            &[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Well-hydrated & well-fed & well-rested + high novelty → very strong explore
        data_set.push(
            &[0.9, 0.9, 0.9, 0.5, 0.9, 0.2, 0.8, 0.3, 0.5, 0.3, 0.7, 0.9],
            &[0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Moderate needs + moderate novelty → moderate explore
        data_set.push(
            &[0.6, 0.6, 0.6, 0.5, 0.6, 0.5, 0.8, 0.3, 0.5, 0.3, 0.7, 0.9],
            &[0.0, 0.0, 0.0, 0.6, 0.0, 0.0, 0.0, 0.0],
        );
        // Low needs → no explore
        data_set.push(
            &[0.2, 0.2, 0.2, 0.5, 0.8, 0.2, 0.8, 0.3, 0.5, 0.3, 0.7, 0.9],
            &[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );

        // 5. Multi-feature competition scenarios
        // Moderate thirst + low stamina → prioritize drink over rest
        data_set.push(
            &[0.4, 0.8, 0.3, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.7, 0.0, 0.3, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Low thirst + moderate stamina → prioritize rest over drink
        data_set.push(
            &[0.7, 0.8, 0.4, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.3, 0.0, 0.7, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Moderate hunger + low thirst → prioritize eat over drink
        data_set.push(
            &[0.7, 0.4, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.3, 0.7, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Low hunger + moderate thirst → prioritize drink over eat
        data_set.push(
            &[0.4, 0.7, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.7, 0.3, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );

        // 6. Time of day modulation
        // Low stamina early day → mild rest
        data_set.push(
            &[0.7, 0.7, 0.2, 0.2, 0.6, 0.4, 0.6, 0.5, 0.4, 0.5, 0.2, 0.8],
            &[0.0, 0.0, 0.6, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Low stamina late day → strong rest
        data_set.push(
            &[0.7, 0.7, 0.2, 0.9, 0.4, 0.4, 0.4, 0.3, 0.6, 0.5, 0.2, 0.8],
            &[0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Early day + well-rested → explore
        data_set.push(
            &[0.8, 0.8, 0.8, 0.2, 0.7, 0.3, 0.7, 0.5, 0.5, 0.5, 0.5, 0.8],
            &[0.0, 0.0, 0.0, 0.8, 0.0, 0.0, 0.0, 0.0],
        );
        // Late day + well-rested → mild explore, some rest
        data_set.push(
            &[0.8, 0.8, 0.8, 0.9, 0.7, 0.3, 0.7, 0.5, 0.5, 0.5, 0.5, 0.8],
            &[0.0, 0.0, 0.3, 0.5, 0.0, 0.0, 0.0, 0.0],
        );

        // 7. Edge cases and noise examples
        // Borderline thirst (0.51) → slight drink
        data_set.push(
            &[0.51, 0.8, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // Borderline hunger (0.49) → slight eat
        data_set.push(
            &[0.8, 0.49, 0.8, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.0, 0.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );
        // All needs partially satisfied → balanced response
        data_set.push(
            &[0.6, 0.6, 0.6, 0.5, 0.6, 0.5, 0.7, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.2, 0.2, 0.2, 0.4, 0.0, 0.0, 0.0, 0.0],
        );
        // All needs critical → prioritize most critical (thirst)
        data_set.push(
            &[0.1, 0.2, 0.2, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5],
            &[0.8, 0.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
        );

        // 8. Threat reactions (keeping these for completeness but not focusing on them)
        // Predator very close → flee
        data_set.push(
            &[0.8, 0.8, 0.8, 0.5, 0.3, 0.2, 0.1, 0.2, 0.4, 0.5, 0.3, 0.7],
            &[0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0],
        );
        // Predator at medium range + hunger high → attack
        data_set.push(
            &[0.5, 0.2, 0.8, 0.5, 0.6, 0.3, 0.5, 0.6, 0.4, 0.2, 0.6, 0.8],
            &[0.0, 1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0],
        );

        // 9. Hiding and patrol (keeping these for completeness but not focusing on them)
        // In cover + threat nearby → hide
        data_set.push(
            &[0.9, 0.9, 0.9, 0.5, 0.5, 0.5, 0.3, 0.4, 0.1, 0.5, 0.6, 0.3],
            &[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0],
        );
        // No threat + low novelty + resource nearby → patrol
        data_set.push(
            &[0.9, 0.9, 0.9, 0.5, 0.1, 0.2, 0.8, 0.7, 0.7, 0.4, 0.9, 0.8],
            &[0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1.0],
        );

        // Train the network with base data
        network.train(&data_set, 50_000);
    }

    // Get base input value
    pub fn get_base_input(&self, input_type: BaseInputType) -> Option<f64> {
        self.input_config
            .base_mapping
            .get(&input_type)
            .map(|&idx| self.inputs[idx])
    }

    // Set base input value
    pub fn set_base_input(&mut self, input_type: BaseInputType, value: f64) {
        if let Some(&idx) = self.input_config.base_mapping.get(&input_type) {
            self.inputs[idx] = value;
        }
    }

    // Get extended input value
    pub fn get_extended_input(&self, name: &str) -> Option<f64> {
        self.input_config
            .extended_mapping
            .get(name)
            .map(|&idx| self.inputs[idx])
    }

    // Set extended input value
    pub fn set_extended_input(&mut self, name: &str, value: f64) {
        if let Some(&idx) = self.input_config.extended_mapping.get(name) {
            self.inputs[idx] = value;
        }
    }

    // Get base output value
    pub fn get_base_output(&self, output_type: BaseOutputType) -> Option<f64> {
        self.output_config
            .base_mapping
            .get(&output_type)
            .map(|&idx| self.outputs[idx])
    }

    // Get extended output value
    pub fn get_extended_output(&self, name: &str) -> Option<f64> {
        self.output_config
            .extended_mapping
            .get(name)
            .map(|&idx| self.outputs[idx])
    }

    // Calculate outputs from current inputs
    pub fn calculate_outputs(&mut self) {
        self.outputs = self.network.calculate(&self.inputs).to_vec();
    }
}
