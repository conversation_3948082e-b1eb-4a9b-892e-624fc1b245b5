use crate::components::animals::{
    AnimalMoveToTargetParams, AnimalMovementBehavior, BiologicalOrganism, LastVisitedPlants, Lifespan, Pollinator
};
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::math::Vec3;
use bevy::prelude::{ReflectComponent, *};
use noise::NoiseFn;
use rand::Rng;

#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
#[relationship_target(relationship = HomeBeeHive)]
pub struct BeeHive {
    pub nectar_stored: f32,
    pub max_nectar: f32,
    pub honey_production_rate: f32,
    pub is_producing: bool,
    pub current_honey: f32,
    pub max_honey: f32,
    pub max_workers: usize,
    pub worker_spawn_rate: f32,
    pub honey_cost_per_worker_to_spawn: f32,
    #[reflect(ignore)]
    #[relationship]
    bees: Vec<Entity>,
}

impl Default for BeeHive {
    fn default() -> Self {
        Self {
            bees: Vec::new(),
            nectar_stored: 0.0,
            max_nectar: 100.0,
            honey_production_rate: 0.1,
            is_producing: false,
            current_honey: 20.0,
            max_honey: 100.0,
            max_workers: 10,
            worker_spawn_rate: 0.1,
            honey_cost_per_worker_to_spawn: 5.0,
        }
    }
}

impl BeeHive {
    pub fn get_bees_count(&self) -> usize {
        self.bees.len()
    }
}

#[derive(Component, Debug, Clone)]
pub struct TreeHasBeeHive;

#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
#[relationship(relationship_target = BeeHive)]
#[require(Name::new("BeeHive"))]
pub struct HomeBeeHive(pub Entity);

#[derive(Reflect, Component, Debug, Clone)]
#[reflect(Component)]
#[require(
    Name::new("Bee"),
    LastVisitedPlants,
    BiologicalOrganism,
    Pollinator,
    Transform
)]
pub struct Bee {
    pub nectar_carried: f32,
    pub max_nectar: f32,
    pub acceleration_speed: f32,
    pub pollination_range: f32,
    pub target_distance: f32,
    pub slowing_radius: f32, // Distance to start slowing
    #[reflect(ignore)]
    pub noise: noise::Perlin, // Perlin noise generator
    pub noise_time: f64,     // Time for noise sampling
    pub noise_seed: f64,     // Unique seed per bee
    pub noise_freq: f64,     // Frequency of noise sampling
    pub noise_amp: f32,      // Amplitude of wander
}

impl Default for Bee {
    fn default() -> Self {
        let mut rng = rand::thread_rng();
        let seed = rng.gen::<u32>();
        let pollination_range = 0.3;
        Self {
            nectar_carried: 0.0,
            max_nectar: 1.0,
            target_distance: 0.3,
            acceleration_speed: 1.0,
            pollination_range,
            slowing_radius: 1.0,
            noise: noise::Perlin::new(seed), // default seed
            noise_seed: seed as f64,         // unique per bee
            noise_freq: 0.5,                 // moderate wander speed
            noise_amp: 5.0,                  // wander magnitude
            noise_time: 0.0,
        }
    }
}

impl Bee {
    pub fn move_randomly_in_place(&mut self, dt: f32) -> Vec3 {
        self.noise_time += dt as f64;
        let noise_amp = 0.9 * self.acceleration_speed as f64;
        let noise_x = self.noise.get([self.noise_time * noise_amp, 0.0]) as f32;
        let noise_y = self.noise.get([self.noise_time * noise_amp, 0.5]) as f32;
        let noise_z = self.noise.get([self.noise_time * noise_amp, 1.0]) as f32;
        Vec3::new(noise_x, noise_y, noise_z)
    }

    pub fn reset_noise(&mut self) {
        self.noise_time = 0.0;
    }

    fn calculate_velocity(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
        speed: f32,
    ) -> (Vec3, Vec3) {
        self.noise_time += dt as f64;
        let noise_x =
            self.noise
                .get([self.noise_time * self.noise_amp as f64, 0.0]) as f32;
        let noise_y =
            self.noise
                .get([self.noise_time * self.noise_amp as f64, 0.5]) as f32;
        let noise_z =
            self.noise
                .get([self.noise_time * self.noise_amp as f64, 1.0]) as f32;

        let noise_offset = Vec3::new(noise_x, noise_y, noise_z);
        let target_direction = (target - current).normalize();

        // Calculate desired velocity with noise offset for natural movement
        (
            (target_direction + noise_offset * self.noise_freq as f32).normalize()
                * speed
                * dt,
            target_direction,
        )
    }

    /// Calculate velocity to target with added noise for natural movement
    pub fn calculate_velocity_towards_target(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
    ) -> (Vec3, Vec3) {
        self.calculate_velocity(dt, target, current, self.acceleration_speed)
    }

    pub fn calculate_velocity_towards_target_with_speed(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
        speed: f32,
    ) -> (Vec3, Vec3) {
        self.calculate_velocity(dt, target, current, speed)
    }
}

impl AnimalMovementBehavior for Bee {
    fn get_target_distance(&self) -> f32 {
        self.target_distance
    }

    fn calculate_velocity_towards_target(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
    ) -> (Vec3, Vec3) {
        self.calculate_velocity_towards_target(dt, target, current)
    }

    fn calculate_velocity_towards_target_with_speed(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
        speed: f32,
    ) -> (Vec3, Vec3) {
        self.calculate_velocity_towards_target_with_speed(dt, target, current, speed)
    }

    fn get_speed(&self) -> f32 {
        self.acceleration_speed
    }

    fn move_towards_target(
        &mut self,
        transform: &mut GlobalTransform,
        linear_velocity: &mut LinearVelocity,
        angular_velocity: &mut AngularVelocity,
        params: AnimalMoveToTargetParams,
    ) {
        let (desired_velocity, target_direction) = self
            .calculate_velocity_towards_target(
                params.delta,
                params.target,
                transform.translation(),
            );

        linear_velocity.0 = desired_velocity;
        angular_velocity.0 = Vec3::ZERO;
        // transform.look_to(target_direction, Vec3::Y);
    }
}
