use bevy::prelude::*;
use crate::components::animals::AnimalAnimationIndex;

/// Component that tracks the current animation state of an organism
#[derive(Component, Reflect, Debug, Clone, PartialEq)]
#[reflect(Component)]
pub struct AnimationState {
    /// The current animation being played
    pub current_animation: OrganismAnimation,
    /// The previous animation (for transition logic)
    pub previous_animation: Option<OrganismAnimation>,
    /// Time since the current animation started
    pub time_in_current_animation: f32,
    /// Whether the animation should loop
    pub should_loop: bool,
    /// Speed multiplier for the animation
    pub speed_multiplier: f32,
}

impl Default for AnimationState {
    fn default() -> Self {
        Self {
            current_animation: OrganismAnimation::Idle,
            previous_animation: None,
            time_in_current_animation: 0.0,
            should_loop: true,
            speed_multiplier: 1.0,
        }
    }
}

impl AnimationState {
    pub fn new(animation: OrganismAnimation) -> Self {
        Self {
            current_animation: animation,
            ..default()
        }
    }

    pub fn transition_to(&mut self, new_animation: OrganismAnimation) {
        if self.current_animation != new_animation {
            self.previous_animation = Some(self.current_animation.clone());
            self.current_animation = new_animation;
            self.time_in_current_animation = 0.0;
        }
    }

    pub fn update_time(&mut self, delta_time: f32) {
        self.time_in_current_animation += delta_time;
    }
}

/// Enum representing different animation states for organisms
#[derive(Debug, Clone, PartialEq, Reflect)]
pub enum OrganismAnimation {
    /// Organism is idle (standing still)
    Idle,
    /// Organism is sitting
    Sit,
    /// Organism is laying down
    Lay,
    /// Organism is walking
    Walk,
    /// Organism is running
    Run,
    /// Organism is swimming
    Swim,
    /// Organism is eating
    Eat,
    /// Organism is dying/dead
    Death,
    /// Organism is attacking
    Attack,
    /// Organism is in fear state
    Fear,
    /// Organism is jumping
    Jump,
    /// Organism is flying (for bees)
    Fly,
    /// Organism is turning left
    TurnLeft,
    /// Organism is turning right
    TurnRight,
}

impl OrganismAnimation {
    /// Get the animation index for this animation type
    pub fn get_animation_index(&self) -> usize {
        match self {
            OrganismAnimation::Idle => AnimalAnimationIndex::IDLE_A,
            OrganismAnimation::Sit => AnimalAnimationIndex::SIT,
            OrganismAnimation::Lay => AnimalAnimationIndex::SIT, // Use sit for lay if no lay animation
            OrganismAnimation::Walk => AnimalAnimationIndex::WALK,
            OrganismAnimation::Run => AnimalAnimationIndex::RUN,
            OrganismAnimation::Swim => AnimalAnimationIndex::SWIM,
            OrganismAnimation::Eat => AnimalAnimationIndex::EAT,
            OrganismAnimation::Death => AnimalAnimationIndex::DEATH,
            OrganismAnimation::Attack => AnimalAnimationIndex::ATTACK,
            OrganismAnimation::Fear => AnimalAnimationIndex::FEAR,
            OrganismAnimation::Jump => AnimalAnimationIndex::JUMP,
            OrganismAnimation::Fly => AnimalAnimationIndex::FLY,
            OrganismAnimation::TurnLeft => AnimalAnimationIndex::TURN_LEFT,
            OrganismAnimation::TurnRight => AnimalAnimationIndex::TURN_RIGHT,
        }
    }

    /// Get the priority of this animation (higher number = higher priority)
    pub fn get_priority(&self) -> u8 {
        match self {
            OrganismAnimation::Death => 100,
            OrganismAnimation::Attack => 90,
            OrganismAnimation::Fear => 85,
            OrganismAnimation::Eat => 80,
            OrganismAnimation::Jump => 75,
            OrganismAnimation::Swim => 70,
            OrganismAnimation::Run => 60,
            OrganismAnimation::Walk => 50,
            OrganismAnimation::Fly => 45,
            OrganismAnimation::TurnLeft => 40,
            OrganismAnimation::TurnRight => 40,
            OrganismAnimation::Sit => 20,
            OrganismAnimation::Lay => 15,
            OrganismAnimation::Idle => 10,
        }
    }

    /// Check if this animation should loop
    pub fn should_loop(&self) -> bool {
        match self {
            OrganismAnimation::Death => false,
            OrganismAnimation::Jump => false,
            OrganismAnimation::Attack => false,
            OrganismAnimation::TurnLeft => true,  // Turn animations should loop while turning
            OrganismAnimation::TurnRight => true,
            _ => true,
        }
    }
}

/// Configuration for animation speed thresholds
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct AnimationConfig {
    /// Velocity threshold below which the organism is considered idle
    pub idle_threshold: f32,
    /// Velocity threshold above which the organism switches from walk to run
    pub run_threshold: f32,
    /// Base speed multiplier for walk animations
    pub walk_speed_multiplier: f32,
    /// Base speed multiplier for run animations
    pub run_speed_multiplier: f32,
    /// How often to randomly change idle animations (in seconds)
    pub idle_variation_interval: f32,
    /// Time since last idle variation change
    pub time_since_idle_change: f32,
}

impl Default for AnimationConfig {
    fn default() -> Self {
        Self {
            idle_threshold: 0.01,
            run_threshold: 3.0,
            walk_speed_multiplier: 1.0,
            run_speed_multiplier: 1.5,
            idle_variation_interval: 10.0,
            time_since_idle_change: 0.0,
        }
    }
}

impl AnimationConfig {
    pub fn new(idle_threshold: f32, run_threshold: f32) -> Self {
        Self {
            idle_threshold,
            run_threshold,
            ..default()
        }
    }

    pub fn update_idle_timer(&mut self, delta_time: f32) {
        self.time_since_idle_change += delta_time;
    }

    pub fn should_change_idle_animation(&mut self) -> bool {
        if self.time_since_idle_change >= self.idle_variation_interval {
            self.time_since_idle_change = 0.0;
            true
        } else {
            false
        }
    }
}

/// Component that tracks direction changes for turn animation blending
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct DirectionTracker {
    /// Previous movement direction (normalized)
    pub previous_direction: Vec3,
    /// Current movement direction (normalized)
    pub current_direction: Vec3,
    /// Angular velocity of direction change (radians per second)
    pub angular_velocity: f32,
    /// Threshold for detecting significant direction changes
    pub turn_threshold: f32,
    /// How long the turn has been happening
    pub turn_duration: f32,
    /// Maximum duration for turn blending
    pub max_turn_duration: f32,
    /// Whether we're currently in a turning state
    pub is_turning: bool,
    /// Turn direction: -1.0 for left, 1.0 for right, 0.0 for no turn
    pub turn_direction: f32,
}

impl Default for DirectionTracker {
    fn default() -> Self {
        Self {
            previous_direction: Vec3::ZERO,
            current_direction: Vec3::ZERO,
            angular_velocity: 0.0,
            turn_threshold: 30.0, // degrees per second
            turn_duration: 0.0,
            max_turn_duration: 1.0, // 1 second max turn blend
            is_turning: false,
            turn_direction: 0.0,
        }
    }
}

impl DirectionTracker {
    pub fn new(turn_threshold: f32, max_turn_duration: f32) -> Self {
        Self {
            turn_threshold,
            max_turn_duration,
            ..default()
        }
    }

    pub fn update(&mut self, new_direction: Vec3, delta_time: f32) {
        if new_direction.length() < 0.01 {
            // Not moving, reset turning state
            self.is_turning = false;
            self.turn_duration = 0.0;
            self.angular_velocity = 0.0;
            return;
        }

        let normalized_direction = new_direction.normalize();

        // Update previous direction if we have a valid current direction
        if self.current_direction.length() > 0.01 {
            self.previous_direction = self.current_direction;
        }

        self.current_direction = normalized_direction;

        // Calculate angular velocity if we have a previous direction
        if self.previous_direction.length() > 0.01 {
            let angle_diff = self.previous_direction.angle_between(self.current_direction);
            self.angular_velocity = angle_diff / delta_time;

            // Determine turn direction using cross product
            let cross = self.previous_direction.cross(self.current_direction);
            self.turn_direction = if cross.y > 0.0 { 1.0 } else { -1.0 };

            // Check if we're turning significantly
            let angular_velocity_degrees = self.angular_velocity.to_degrees();

            if angular_velocity_degrees > self.turn_threshold {
                if !self.is_turning {
                    self.is_turning = true;
                    self.turn_duration = 0.0;
                }
                self.turn_duration += delta_time;
            } else {
                // Gradually reduce turning state
                if self.is_turning {
                    self.turn_duration -= delta_time * 2.0; // Fade out faster than fade in
                    if self.turn_duration <= 0.0 {
                        self.is_turning = false;
                        self.turn_duration = 0.0;
                    }
                }
            }

            // Clamp turn duration
            self.turn_duration = self.turn_duration.min(self.max_turn_duration);
        }
    }

    /// Get the blend weight for turn animations (0.0 to 1.0)
    pub fn get_turn_blend_weight(&self) -> f32 {
        if !self.is_turning {
            return 0.0;
        }

        // Smooth blend in/out using smoothstep
        let normalized_duration = (self.turn_duration / self.max_turn_duration).min(1.0);
        normalized_duration * normalized_duration * (3.0 - 2.0 * normalized_duration)
    }

    /// Get which turn animation to use
    pub fn get_turn_animation(&self) -> Option<OrganismAnimation> {
        if !self.is_turning || self.get_turn_blend_weight() < 0.1 {
            return None;
        }

        if self.turn_direction > 0.0 {
            Some(OrganismAnimation::TurnRight)
        } else {
            Some(OrganismAnimation::TurnLeft)
        }
    }
}

/// Component for managing animation graph blending
#[derive(Component, Reflect, Debug, Clone)]
#[reflect(Component)]
pub struct AnimationBlendState {
    /// Handle to the animation graph
    pub animation_graph: Option<Handle<AnimationGraph>>,
    /// Current blend weights for different animation layers
    pub blend_weights: Vec<f32>,
    /// Whether blending is currently active
    pub is_blending: bool,
    /// Time spent in current blend state
    pub blend_time: f32,
    /// Duration for blend transitions
    pub blend_transition_duration: f32,
}

impl Default for AnimationBlendState {
    fn default() -> Self {
        Self {
            animation_graph: None,
            blend_weights: vec![1.0, 0.0, 0.0], // Base, Turn Left, Turn Right
            is_blending: false,
            blend_time: 0.0,
            blend_transition_duration: 0.3, // 300ms blend transition
        }
    }
}

impl AnimationBlendState {
    pub fn new(blend_transition_duration: f32) -> Self {
        Self {
            blend_transition_duration,
            ..default()
        }
    }

    pub fn with_animation_graph(mut self, animation_graph: Handle<AnimationGraph>) -> Self {
        self.animation_graph = Some(animation_graph);
        self
    }

    pub fn update_blend_weights(&mut self, base_weight: f32, turn_left_weight: f32, turn_right_weight: f32, delta_time: f32) {
        let target_weights = vec![base_weight, turn_left_weight, turn_right_weight];

        // Check if we need to start blending
        let weights_changed = self.blend_weights.iter().zip(target_weights.iter())
            .any(|(current, target)| (current - target).abs() > 0.01);

        if weights_changed {
            self.is_blending = true;
            self.blend_time = 0.0;
        }

        if self.is_blending {
            self.blend_time += delta_time;
            let blend_progress = (self.blend_time / self.blend_transition_duration).min(1.0);

            // Smooth interpolation
            let smooth_progress = blend_progress * blend_progress * (3.0 - 2.0 * blend_progress);

            for (i, (current, target)) in self.blend_weights.iter_mut().zip(target_weights.iter()).enumerate() {
                *current = current.lerp(*target, smooth_progress);
            }

            // Stop blending when we're close enough
            if blend_progress >= 1.0 {
                self.is_blending = false;
                self.blend_weights = target_weights;
            }
        }
    }

    pub fn get_base_weight(&self) -> f32 {
        self.blend_weights.get(0).copied().unwrap_or(1.0)
    }

    pub fn get_turn_left_weight(&self) -> f32 {
        self.blend_weights.get(1).copied().unwrap_or(0.0)
    }

    pub fn get_turn_right_weight(&self) -> f32 {
        self.blend_weights.get(2).copied().unwrap_or(0.0)
    }
}
