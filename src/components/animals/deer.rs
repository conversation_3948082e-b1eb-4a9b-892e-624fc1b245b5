use crate::components::animals::{AnimalMovementBehavior, BiologicalOrganism};
use bevy::prelude::*;

#[derive(Component, Reflect, Clone, Copy)]
#[reflect(Component)]
#[require(Name::new("Deer"), BiologicalOrganism, Transform)]
pub struct Deer {
    pub acceleration_speed: f32,
    pub target_distance: f32,
    pub slowing_radius: f32, // Distance to start slowing
}

impl Default for Deer {
    fn default() -> Self {
        Self {
            acceleration_speed: 1.0,
            target_distance: 0.1,
            slowing_radius: 1.0,
        }
    }
}

impl AnimalMovementBehavior for Deer {
    fn get_target_distance(&self) -> f32 {
        self.target_distance
    }

    fn get_speed(&self) -> f32 {
        self.acceleration_speed
    }

    fn calculate_velocity_towards_target(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
    ) -> (Vec3, Vec3) {
        self.calculate_velocity_towards_target_with_speed(
            dt,
            target,
            current,
            self.acceleration_speed,
        )
    }

    fn calculate_velocity_towards_target_with_speed(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
        speed: f32,
    ) -> (Vec3, Vec3) {
        let direction = (target - current).normalize();
        let distance = direction.length();

        let desired_velocity = if distance > self.slowing_radius {
            // Move at max speed
            direction.normalize() * speed * dt
        } else {
            // Slow down as we approach
            direction.normalize() * speed * (distance / self.slowing_radius) * dt
        };

        (desired_velocity, direction)
    }
}

crate::animal_base_armature!(
    DeerQuadrupedBaseArmature,
    "deer",
    "Root",
    spine = ["spine.003", "spine.004", "spine.005"],
    neck  = ["spine.006", "spine.007", "spine.008"],
    tail  = ["spine.003", "spine.002", "spine.001", "spine"]
);


