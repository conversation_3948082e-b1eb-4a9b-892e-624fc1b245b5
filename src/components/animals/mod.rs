pub mod animation_state;
pub mod bees;
pub mod deer;

use crate::components::lifecycle::*;
use avian3d::math::<PERSON>ala<PERSON>;
use avian3d::prelude::{AngularVelocity, LinearVelocity};
use bevy::prelude::*;
use bevy_descendant_collector::EntityCollectorTarget;
use bevy_inspector_egui::prelude::*;
use bevy_inspector_egui::InspectorOptions;
use std::marker::PhantomData;

/// Parameters for moving an animal towards a target
#[derive(Default, Component, Reflect, <PERSON><PERSON>, Co<PERSON>)]
pub struct AnimalMoveToTargetParams {
    /// Maximum turning speed in radians per second
    pub max_turn_speed: f32,
    /// How quickly the organism can change its turning speed (angular acceleration)
    pub turn_acceleration: f32,
    /// Current speed of the organism
    pub current_speed: f32,
    /// Target position to move towards
    pub target: Vec3,
    /// Delta time for movement
    pub delta: f32,
}

pub trait AnimalMovementBehavior {
    fn get_target_distance(&self) -> f32;

    fn get_speed(&self) -> f32 {
        1.0
    }

    fn calculate_velocity_towards_target(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
    ) -> (Vec3, Vec3) {
        self.calculate_velocity_towards_target_with_speed(dt, target, current, 1.0)
    }

    fn calculate_velocity_towards_target_with_speed(
        &mut self,
        dt: f32,
        target: Vec3,
        current: Vec3,
        speed: f32,
    ) -> (Vec3, Vec3);

    /// Move the animal towards the target
    fn move_towards_target(
        &mut self,
        transform: &mut GlobalTransform,
        linear_velocity: &mut LinearVelocity,
        angular_velocity: &mut AngularVelocity,
        params: AnimalMoveToTargetParams,
    ) {
        // Calculate direction to target
        let to_target = (params.target - transform.translation()).normalize();
        let current_forward = transform.forward().normalize();

        // Calculate the angle difference between current direction and target direction
        let angle_to_target = current_forward.angle_between(to_target);

        // Determine turn direction using cross product
        let cross_product = current_forward.cross(to_target);
        let turn_direction = if cross_product.y > 0.0 { 1.0 } else { -1.0 };

        // Calculate desired angular velocity for steering
        let desired_angular_velocity = if angle_to_target > 0.01 {
            // Scale turn speed based on how much we need to turn
            let turn_urgency = (angle_to_target / std::f32::consts::PI).min(1.0);
            turn_direction * params.max_turn_speed * turn_urgency
        } else {
            0.0
        };

        // Apply angular acceleration to smoothly change turning speed
        let current_angular_speed = angular_velocity.0.y;
        let angular_speed_diff = desired_angular_velocity - current_angular_speed;
        let max_angular_change = params.turn_acceleration * params.delta;

        let new_angular_speed = current_angular_speed
            + angular_speed_diff.clamp(-max_angular_change, max_angular_change);

        angular_velocity.0 = Vec3::new(0.0, new_angular_speed, 0.0);

        // Move forward in the direction the organism is currently facing
        // Reduce speed when turning sharply to simulate realistic movement
        let turn_speed_factor =
            1.0 - (angle_to_target / std::f32::consts::PI * 0.5).min(0.7);
        let forward_speed = params.current_speed * turn_speed_factor;
        linear_velocity.0 = current_forward * forward_speed * params.delta;
    }
}

#[derive(Component, Reflect)]
#[reflect(Component)]
pub struct MovementDampingFactor(pub Scalar);

#[derive(Component)]
pub struct AutoRigColliders;

#[derive(Component)]
#[component(storage = "SparseSet")]
pub struct AutoSetupIK;

#[derive(Component)]
pub struct AnimalSpeciesModelSet;

#[derive(Reflect, Component, Clone, Copy, Debug, PartialEq, Eq, Hash)]
#[reflect(Component, Hash)]
pub enum AnimalSpeciesType {
    Bee,
    Pudu,
    Deer,
}

#[derive(Reflect, Component, Clone, Debug)]
#[reflect(Component)]
pub struct AnimalSpeciesModelKey(pub String);

impl AnimalSpeciesModelKey {
    pub fn new(name: &str) -> Self {
        Self(name.to_string())
    }
}

pub trait AnimalBaseArmature {
    fn get_root(&self) -> Entity;
    fn get_mesh(&self) -> Entity;
    fn get_toe_l(&self) -> Entity;
    fn get_toe_r(&self) -> Entity;
    fn get_foot_l(&self) -> Entity;
    fn get_foot_r(&self) -> Entity;
    fn get_shin_l(&self) -> Entity;
    fn get_shin_r(&self) -> Entity;
    fn get_thigh_l(&self) -> Entity;
    fn get_thigh_r(&self) -> Entity;
    fn get_shoulder_l(&self) -> Entity;
    fn get_shoulder_r(&self) -> Entity;
    fn get_front_toe_l(&self) -> Entity;
    fn get_front_toe_r(&self) -> Entity;
}

#[macro_export]
macro_rules! animal_base_armature {
    (
        $name:ident,
        $scene_root_name:literal,
        $root_bone_name:literal,
        spine = [ $first_spine:literal $(, $spine_tail:literal)* $(,)? ],
        neck  = [ $($neck:literal),+  $(,)? ],
        tail  = [ $($tail:literal),+  $(,)? ]
    ) => {
        #[derive(
            bevy::prelude::Component,
            bevy_descendant_collector::EntityCollectorTarget,
            bevy::reflect::Reflect
        )]
        #[reflect(Component)]
        #[name_path($scene_root_name)]
        pub struct $name {
            #[name_path($root_bone_name)]
            pub root: bevy::prelude::Entity,

            // HEAD & EARS (full neck chain plus skull/ears)
            #[name_path($root_bone_name, $first_spine $(, $spine_tail)*, $($neck),*, "scull")]
            pub head: bevy::prelude::Entity,
            #[name_path($root_bone_name, $first_spine $(, $spine_tail)*, $($neck),*, "scull", "ear.L")]
            pub ear_l: bevy::prelude::Entity,
            #[name_path($root_bone_name, $first_spine $(, $spine_tail)*, $($neck),*, "scull", "ear.R")]
            pub ear_r: bevy::prelude::Entity,

            // FRONT LIMBS (full spine prefixes, then each joint in order)
            #[name_path($root_bone_name, $first_spine $(, $spine_tail)*, "front_shoulder.L")]
            pub front_shoulder_l: bevy::prelude::Entity,
            #[name_path($root_bone_name, $first_spine $(, $spine_tail)*, "front_shoulder.R")]
            pub front_shoulder_r: bevy::prelude::Entity,

            #[name_path(
                $root_bone_name, $first_spine $(, $spine_tail)*,
                "front_shoulder.L", "front_thigh.L"
            )]
            pub front_thigh_l: bevy::prelude::Entity,
            #[name_path(
                $root_bone_name, $first_spine $(, $spine_tail)*,
                "front_shoulder.R", "front_thigh.R"
            )]
            pub front_thigh_r: bevy::prelude::Entity,

            #[name_path(
                $root_bone_name, $first_spine $(, $spine_tail)*,
                "front_shoulder.L", "front_thigh.L", "front_shin.L"
            )]
            pub front_shin_l: bevy::prelude::Entity,
            #[name_path(
                $root_bone_name, $first_spine $(, $spine_tail)*,
                "front_shoulder.R", "front_thigh.R", "front_shin.R"
            )]
            pub front_shin_r: bevy::prelude::Entity,

            #[name_path(
                $root_bone_name, $first_spine $(, $spine_tail)*,
                "front_shoulder.L", "front_thigh.L", "front_shin.L", "front_foot.L"
            )]
            pub front_foot_l: bevy::prelude::Entity,
            #[name_path(
                $root_bone_name, $first_spine $(, $spine_tail)*,
                "front_shoulder.R", "front_thigh.R", "front_shin.R", "front_foot.R"
            )]
            pub front_foot_r: bevy::prelude::Entity,

            #[name_path(
                $root_bone_name, $first_spine $(, $spine_tail)*,
                "front_shoulder.L", "front_thigh.L", "front_shin.L",
                "front_foot.L", "front_toe.L"
            )]
            pub front_toe_l: bevy::prelude::Entity,
            #[name_path(
                $root_bone_name, $first_spine $(, $spine_tail)*,
                "front_shoulder.R", "front_thigh.R", "front_shin.R",
                "front_foot.R", "front_toe.R"
            )]
            pub front_toe_r: bevy::prelude::Entity,

            // REAR LIMBS (shallow spine prefix, then each joint)
            #[name_path($root_bone_name, $first_spine, "shoulder.L")]
            pub shoulder_l: bevy::prelude::Entity,
            #[name_path($root_bone_name, $first_spine, "shoulder.R")]
            pub shoulder_r: bevy::prelude::Entity,

            #[name_path($root_bone_name, $first_spine, "shoulder.L", "thigh.L")]
            pub thigh_l: bevy::prelude::Entity,
            #[name_path($root_bone_name, $first_spine, "shoulder.R", "thigh.R")]
            pub thigh_r: bevy::prelude::Entity,

            #[name_path($root_bone_name, $first_spine, "shoulder.L", "thigh.L", "shin.L")]
            pub shin_l: bevy::prelude::Entity,
            #[name_path($root_bone_name, $first_spine, "shoulder.R", "thigh.R", "shin.R")]
            pub shin_r: bevy::prelude::Entity,

            #[name_path($root_bone_name, $first_spine, "shoulder.L", "thigh.L", "shin.L", "foot.L")]
            pub foot_l: bevy::prelude::Entity,
            #[name_path($root_bone_name, $first_spine, "shoulder.R", "thigh.R", "shin.R", "foot.R")]
            pub foot_r: bevy::prelude::Entity,

            #[name_path(
                $root_bone_name, $first_spine,
                "shoulder.L", "thigh.L", "shin.L", "foot.L", "toe.L"
            )]
            pub toe_l: bevy::prelude::Entity,
            #[name_path(
                $root_bone_name, $first_spine,
                "shoulder.R", "thigh.R", "shin.R", "foot.R", "toe.R"
            )]
            pub toe_r: bevy::prelude::Entity,

            // TAIL (full tail prefixes, then the base)
            #[name_path($root_bone_name, $($tail),*)]
            pub tail: bevy::prelude::Entity,
        }

        impl crate::components::animals::AnimalBaseArmature for $name {
            fn get_root(&self) -> Entity {
                self.root
            }
            fn get_mesh(&self) -> Entity {
                self.head
            }
            fn get_toe_l(&self) -> Entity {
                self.toe_l
            }
            fn get_toe_r(&self) -> Entity {
                self.toe_r
            }
            fn get_foot_l(&self) -> Entity {
                self.foot_l
            }
            fn get_foot_r(&self) -> Entity {
                self.foot_r
            }
            fn get_shin_l(&self) -> Entity {
                self.shin_l
            }
            fn get_shin_r(&self) -> Entity {
                self.shin_r
            }
            fn get_thigh_l(&self) -> Entity {
                self.thigh_l
            }
            fn get_thigh_r(&self) -> Entity {
                self.thigh_r
            }
            fn get_shoulder_l(&self) -> Entity {
                self.shoulder_l
            }
            fn get_shoulder_r(&self) -> Entity {
                self.shoulder_r
            }
            fn get_front_toe_l(&self) -> Entity {
                self.front_toe_l
            }
            fn get_front_toe_r(&self) -> Entity {
                self.front_toe_r
            }
        }
    };
}

// impl AnimalBaseArmature
// for AnimalQuadrupedBaseArmature<T: AnimalMovementBehavior>
// {
//     fn get_base(&self) -> Entity {
//         Entity::PLACEHOLDER
//     }
//     fn get_root(&self) -> Entity {
//         Entity::PLACEHOLDER
//     }
//     fn get_body(&self) -> Entity {
//         Entity::PLACEHOLDER
//     }
//     fn get_ear_l(&self) -> Entity {
//         Entity::PLACEHOLDER
//     }
//     fn get_ear_r(&self) -> Entity {
//         Entity::PLACEHOLDER
//     }
//     fn get_mesh(&self) -> Entity {
//         Entity::PLACEHOLDER
//     }
// }

#[allow(non_snake_case, non_upper_case_globals)]
pub mod AnimalAnimationIndex {
    pub const ATTACK: usize = 0;
    pub const BOUNCE: usize = 1;
    pub const CLICKED: usize = 2;
    pub const DEATH: usize = 3;
    pub const EAT: usize = 4;
    pub const FEAR: usize = 5;
    pub const FLY: usize = 6;
    pub const HIT: usize = 7;
    pub const IDLE: usize = 8;
    pub const TURN_LEFT: usize = 9;
    pub const TURN_RIGHT: usize = 10;
    pub const JUMP: usize = 11;
    pub const ROLL: usize = 12;
    pub const RUN: usize = 13;
    pub const SIT: usize = 14;
    pub const SPIN_SPLASH: usize = 15;
    pub const SWIM: usize = 16;
    pub const WALK: usize = 17;
}

#[derive(Component)]
#[component(storage = "SparseSet")]
pub struct Grounded;

#[derive(Component)]
#[component(storage = "SparseSet")]
pub struct InWater;

#[derive(Component, Default)]
pub struct Pollinator;

#[derive(Reflect, Clone, Debug)]
pub struct VisitedPlant {
    pub entity: Entity,
    pub visited_time: f32,
    pub visited_count: u32,
    pub last_nectar_amount: f32,
}

#[derive(Component, Reflect, Clone, Debug, Default)]
#[reflect(Component)]
pub struct LastVisitedPlants(pub Vec<VisitedPlant>);

impl LastVisitedPlants {
    pub fn add(&mut self, entity: Entity, time: f32, last_nectar_amount: f32) {
        // If we already have this plant, update the visited time and count
        if let Some(plant) = self.0.iter_mut().find(|p| p.entity == entity) {
            plant.visited_time = time;
            plant.visited_count += 1;
            plant.last_nectar_amount = last_nectar_amount;
            return;
        }

        // Retain only the last recent plants
        self.0.retain(|p| p.visited_time > time - 60.0);

        self.0.push(VisitedPlant {
            entity,
            visited_time: time,
            visited_count: 1,
            last_nectar_amount,
        });
    }

    pub fn last_visit_time(&self, entity: Entity) -> Option<f32> {
        self.0
            .iter()
            .find(|p| p.entity == entity)
            .map(|p| p.visited_time)
    }

    pub fn get_last_nectar(&self, entity: Entity) -> f32 {
        self.0
            .iter()
            .find(|p| p.entity == entity)
            .map(|p| p.last_nectar_amount)
            .unwrap_or(0.0)
    }
}
