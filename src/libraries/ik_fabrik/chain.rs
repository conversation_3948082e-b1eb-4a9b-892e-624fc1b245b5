use crate::libraries::ik_fabrik::constraint::{
    rotate_and_constrain, Constraint, SwingConstraint, TwistConstraint
};
use bevy::ecs::component::{
    Component, ComponentHook, ComponentMutability, HookContext, StorageType
};
use bevy::ecs::world::DeferredWorld;
use bevy::math::{Affine3A, Vec3A};
use bevy::prelude::*;

const DEFAULT_TARGET_SQ_THRESHOLD: f32 = 0.001;
const DEFAULT_MAX_ITERATIONS: usize = 10;

/// Wrapper for QueryEntityError to simplify IK errors.
#[derive(Clone, Copy)]
pub enum IkChainError {
    IkComponentQueryError,
}

impl std::error::Error for IkChainError {}

impl std::fmt::Display for IkChainError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Failed to query required IK chain components")
    }
}

impl std::fmt::Debug for IkChainError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "IkChainError")
    }
}

impl From<bevy::ecs::query::QueryEntityError> for IkChainError {
    fn from(_: bevy::ecs::query::QueryEntityError) -> Self {
        IkChainError::IkComponentQueryError
    }
}

#[derive(Component)]
pub struct IkTarget;

/// Main component that defines an IK chain. Attach to the end‐effector entity.
#[derive(Reflect)]
pub struct IkChain {
    /// Enable or disable solving this chain.
    pub enabled: bool,
    /// Target world‐space position.
    pub target: Vec3,
    /// Squared distance threshold under which we stop iterating.
    pub threshold: f32,
    /// Max FABRIK iterations per frame.
    pub max_iterations: usize,

    // Internal data populated at insertion time:
    joint_count: usize,
    /// Length of each joint segment, from parent to child.
    joint_lengths: Vec<f32>,
    /// Normal of the root joint, in world space.
    root_normal: Dir3,
}

impl IkChain {
    /// Construct a new chain with `joint_count` links.
    /// Must be ≥ 2 and parent entities need valid GlobalTransform.
    pub fn new(joint_count: usize) -> Self {
        Self {
            enabled: true,
            target: Vec3::ZERO,
            threshold: DEFAULT_TARGET_SQ_THRESHOLD,
            max_iterations: DEFAULT_MAX_ITERATIONS,
            joint_count,
            joint_lengths: Vec::new(),
            root_normal: Dir3::Y,
        }
    }

    /// Solve the chain for this end‐effector `entity`.
    /// - `constraint_q`: optional swing/twist constraints per joint.
    /// - `global_q`: to read each joint’s current GlobalTransform.
    /// - `transform_q`: to write back new local Transforms.
    /// - `parent_q`: to walk up the entity hierarchy.
    pub(crate) fn solve(
        &self,
        entity: Entity,
        constraint_q: &Query<(Option<&SwingConstraint>, Option<&TwistConstraint>)>,
        global_q: &Query<&GlobalTransform>,
        transform_q: &mut Query<&mut Transform>,
        parent_q: &Query<&ChildOf>,
    ) -> Result<(), IkChainError> {
        // Early exit if disabled or already within threshold.
        if !self.enabled {
            return Ok(());
        }

        let gt_end = global_q.get(entity)?; // may error if component missing
        if gt_end.translation().distance_squared(self.target) < self.threshold {
            return Ok(());
        }

        // Gather joint transforms + constraints.
        let mut joint_data = Vec::with_capacity(self.joint_count);
        let mut current = entity;
        for i in 0..self.joint_count {
            // Read any constraints on this joint:
            let (swing_opt, twist_opt) = constraint_q.get(current)?;

            // Read its world transform:
            let gt = global_q.get(current)?;

            let mut constraints: Vec<&dyn Constraint> = Vec::new();

            if let Some(s) = swing_opt {
                constraints.push(s as &dyn Constraint);
            }

            if let Some(t) = twist_opt {
                constraints.push(t as &dyn Constraint);
            }

            // Copy out the GlobalTransform (cheap - it's just a small struct)
            joint_data.push((*gt, constraints));

            // Move up one joint:
            if i < self.joint_count - 1 {
                let parent = parent_q.get(current)?;
                current = parent.parent();
            }
        }

        // FABRIK solve passes:
        for _ in 0..self.max_iterations {
            if let Some((root_gt, _)) = joint_data.last() {
                if self.target.distance_squared(root_gt.translation()) < self.threshold {
                    break;
                }
            }

            self.backward_pass(&mut joint_data);
            self.forward_pass(&mut joint_data);
        }

        // Apply new local Transforms from end to root:
        let mut current = entity;
        for i in 0..(self.joint_count - 1) {
            let mut tr = transform_q.get_mut(current)?;
            let updated = joint_data[i].0.reparented_to(&joint_data[i + 1].0);
            tr.translation = updated.translation;
            tr.rotation = updated.rotation;

            let parent = parent_q.get(current)?;
            current = parent.parent();
        }

        Ok(())
    }

    fn backward_pass(&self, joints: &mut [(GlobalTransform, Vec<&dyn Constraint>)]) {
        // Store original root position to reapply later:
        let root_orig = joints.last().unwrap().0.translation();

        // First joint snaps to target:
        joints[0].0 = Affine3A {
            matrix3: joints[0].0.affine().matrix3,
            translation: Vec3A::from(self.target),
        }
        .into();

        // Move each joint i toward joint i-1 by its length:
        for i in 1..joints.len() {
            let dir: Vec3A = (joints[i].0.translation() - joints[i - 1].0.translation())
                .normalize()
                .into();
            joints[i].0 = Affine3A {
                matrix3: joints[i].0.affine().matrix3,
                translation: joints[i - 1].0.affine().translation
                    + dir * self.joint_lengths[i - 1],
            }
            .into();
        }

        // Re‐snap root back to original:
        let last_idx = joints.len() - 1;
        joints[last_idx].0 = Affine3A {
            matrix3: joints[last_idx].0.affine().matrix3,
            translation: root_orig.into(),
        }
        .into();
    }

    fn forward_pass(&self, joints: &mut [(GlobalTransform, Vec<&dyn Constraint>)]) {
        let end_idx = joints.len() - 2;

        // Place the one‐before‐root joint along the original normal:
        joints[end_idx].0 = Affine3A {
            matrix3: joints[end_idx].0.affine().matrix3,
            translation: joints.last().unwrap().0.affine().translation
                + Vec3A::from(self.root_normal * self.joint_lengths[end_idx]),
        }
        .into();

        // From root down to end, enforce constraints:
        for i in (0..end_idx).rev() {
            let child_gt = joints[i + 1].0;
            let parent_gt = joints[i].0;
            let dir = (parent_gt.translation() - child_gt.translation()).normalize();
            joints[i + 1].0 = rotate_and_constrain(
                dir,
                self.root_normal.into(),
                child_gt,
                &joints[i + 1].1,
            );
            joints[i].0 = Affine3A {
                matrix3: parent_gt.affine().matrix3,
                translation: joints[i + 1].0.affine().translation
                    + Vec3A::from(joints[i + 1].0.up() * self.joint_lengths[i + 1]),
            }
            .into();
        }
    }
}

impl Component for IkChain {
    const STORAGE_TYPE: StorageType = StorageType::Table;
    type Mutability = bevy::ecs::component::Mutable;

    /// Register the insert hook under Bevy 0.16
    fn on_insert() -> Option<ComponentHook> {
        Some(ik_chain_insert_hook)
    }
}

/// Called once, when an IkChain is added, to compute lengths & initial normal.
fn ik_chain_insert_hook(
    mut world: DeferredWorld,
    HookContext { entity, .. }: HookContext,
) {
    // Read the just-inserted component’s joint_count:
    let joint_count = world.get::<IkChain>(entity).unwrap().joint_count;
    assert!(
        joint_count >= 2,
        "IK chain must have at least 2 joints, got {}",
        joint_count
    );

    // Walk up the chain, computing segment lengths and the last normal:
    let mut lengths = Vec::with_capacity(joint_count - 1);
    let mut last_dir = Vec3::ZERO;
    let mut tail = entity;

    for _ in 1..joint_count {
        // Parent entity:
        let parent = world
            .get::<ChildOf>(tail)
            .expect("Missing ChildOf on IK chain joint")
            .parent();

        // World positions:
        let p_pos = world
            .get::<GlobalTransform>(parent)
            .expect("Missing GlobalTransform")
            .translation();

        let t_pos = world
            .get::<GlobalTransform>(tail)
            .expect("Missing GlobalTransform")
            .translation();

        // print name of parent
        if let Some(name) = world.get::<Name>(parent) {
            log::info!("Parent name: {:?}", name);
        }

        let dir = t_pos - p_pos;
        last_dir = dir;
        lengths.push(dir.length());
        tail = parent;
    }

    // Write back into the component:
    let mut chain = world
        .get_mut::<IkChain>(entity)
        .expect("IkChain not found on insert");

    chain.joint_lengths = lengths;
    chain.root_normal = Dir3::new_unchecked(last_dir.normalize_or(Vec3::Y));
}
