use crate::libraries::inverse_kinematics::InverseKinematicsPlugin;
use bevy::prelude::*;

pub mod billboard;
pub mod ik_fabrik;
pub mod inverse_kinematics;

pub struct LibrariesPlugin;

impl Plugin for LibrariesPlugin {
    fn build(&self, app: &mut App) {
        app.add_plugins(billboard::plugin::BillboardPlugin)
            .add_plugins(ik_fabrik::FabrikInverseKinematicsPlugin)
            .add_plugins(InverseKinematicsPlugin);
    }
}
