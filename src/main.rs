mod behaviors;
mod components;
mod events;
mod libraries;
mod materials;
mod plugins;
mod post_process;
mod resources;
mod systems;
mod utils;

use avian3d::prelude::*;
use bevy::dev_tools::fps_overlay::{FpsOverlayConfig, FpsOverlayPlugin};
use bevy_inspector_egui::quick::WorldInspectorPlugin;

use crate::plugins::post_process_plugin::PostProcessingPluginsGroup;
use crate::plugins::*;
use crate::systems::environment::water_physics::WaterPhysicsPlugin;
use bevy::prelude::*;
use bevy::text::FontSmoothing;
use bevy_hanabi::HanabiPlugin;
use bevy_inspector_egui::bevy_egui::EguiPlugin;

fn main() {
    App::new()
        .add_plugins(
            DefaultPlugins
                .set(AssetPlugin {
                    watch_for_changes_override: Some(true),
                    ..Default::default()
                })
                .set(ImagePlugin::default_nearest())
                .set(WindowPlugin {
                    primary_window: Some(Window {
                        title: "Gaia's Crucible".to_string(),
                        ..default()
                    }),
                    ..default()
                }),
        )
        .insert_resource(bevy::winit::WinitSettings {
            focused_mode: bevy::winit::UpdateMode::Continuous,
            unfocused_mode: bevy::winit::UpdateMode::Continuous,
        })
        .add_plugins(FpsOverlayPlugin {
            config: FpsOverlayConfig {
                text_config: TextFont {
                    font_size: 12.0,
                    font: default(),
                    font_smoothing: FontSmoothing::default(),
                    ..default()
                },
                // We can also change color of the overlay
                text_color: Color::srgb(0.0, 1.0, 0.0),
                enabled: true,
                ..default()
            },
        })
        .add_plugins(core_plugin::CorePlugin)
        .add_plugins(PostProcessingPluginsGroup)
        .add_plugins(HanabiPlugin)
        .add_plugins(bevy_obj::ObjPlugin::default())
        .add_plugins(bevy_shader_utils::ShaderUtilsPlugin)
        .add_plugins(PhysicsPlugins::default())
        .add_plugins(WaterPhysicsPlugin)
        // .add_plugins(PhysicsDebugPlugin::default())
        .add_plugins(EguiPlugin {
            enable_multipass_for_primary_context: true,
        })
        .add_plugins(WorldInspectorPlugin::new())
        .add_plugins(bevy_tween::DefaultTweenPlugins)
        .run();
}
